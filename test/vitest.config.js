/**
 * Vitest Configuration for Authentication Tests
 * Comprehensive test configuration for TEST-002 through TEST-010
 */

import { defineConfig } from 'vitest/config'
import { resolve } from 'path'
import { fileURLToPath, URL } from 'node:url'

// Get the directory name in ES modules
const __filename = fileURLToPath(import.meta.url)
const __dirname = resolve(__filename, '..')

// Base configuration for reuse
const baseConfig = {
  test: {
    // Test environment
    environment: 'jsdom',

    // Global setup and teardown
    globalSetup: './test/setup/global-setup.js',
    setupFiles: ['./test/setup/test-setup.js'],

    exclude: [
      'node_modules',
      'dist',
      '.nuxt',
      'coverage',
      'test/fixtures',
    ],

    // Coverage configuration
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      reportsDirectory: './test/coverage',
      include: [
        'ui/stores/**/*.js',
        'ui/composables/**/*.js',
        'ui/middleware/**/*.js',
        'common/services/**/*.js',
        'api/services/**/*.js',
      ],
      exclude: [
        'test/**',
        '**/*.test.js',
        '**/*.spec.js',
        '**/node_modules/**',
        '**/.nuxt/**',
        '**/dist/**',
      ],
      thresholds: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80,
        },
        'ui/stores/auth.js': {
          branches: 90,
          functions: 90,
          lines: 90,
          statements: 90,
        },
        'common/services/pocketbase.js': {
          branches: 85,
          functions: 85,
          lines: 85,
          statements: 85,
        },
      },
    },

    // Test timeout
    testTimeout: 10000,
    hookTimeout: 10000,

    // Reporters
    reporter: ['verbose'],

    // Mock configuration
    server: {
      deps: {
        inline: ['vue', '@vue/test-utils', 'pinia'],
      },
    },

    // Test categorization
    pool: 'threads',
    poolOptions: {
      threads: {
        singleThread: false,
        isolate: true,
      },
    },

    // Retry configuration for flaky tests
    retry: 2,

    // Watch configuration
    watch: false,
  },

  // Resolve configuration
  resolve: {
    alias: {
      '@': resolve(__dirname, '../'),
      '~': resolve(__dirname, '../'),
      'ui': resolve(__dirname, '../ui'),
      'common': resolve(__dirname, '../common'),
      'api': resolve(__dirname, '../api'),
      'test': resolve(__dirname, '../test'),
    },
  },

  // Define configuration for different test types
  define: {
    __TEST__: true,
    __DEV__: true,
  },
}

// Default export using base configuration
export default defineConfig({
  ...baseConfig,
  test: {
    ...baseConfig.test,
    // Test patterns for all tests
    include: [
      'test/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}',
      'ui/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}',
    ],

    // Additional reporters for main config
    reporter: ['verbose', 'json', 'html'],
    outputFile: {
      json: './test/results/test-results.json',
      html: './test/results/test-results.html',
    },

    // Browser configuration for E2E tests
    browser: {
      enabled: false, // Enable for E2E tests
      name: 'chromium',
      provider: 'playwright',
      headless: true,
      screenshotOnFailure: true,
    },
  },
})

// Export test configurations for different test suites
export const unitTestConfig = defineConfig({
  ...baseConfig,
  test: {
    ...baseConfig.test,
    include: [
      'test/unit/**/*.{test,spec}.{js,ts}',
      'ui/stores/__tests__/**/*.{test,spec}.{js,ts}',
    ],
    name: 'unit',
  },
})

export const integrationTestConfig = defineConfig({
  ...baseConfig,
  test: {
    ...baseConfig.test,
    include: [
      'test/integration/**/*.{test,spec}.{js,ts}',
    ],
    name: 'integration',
    testTimeout: 15000,
  },
})

export const e2eTestConfig = defineConfig({
  ...baseConfig,
  test: {
    ...baseConfig.test,
    include: [
      'test/e2e/**/*.{test,spec}.{js,ts}',
    ],
    name: 'e2e',
    testTimeout: 30000,
    browser: {
      enabled: true,
      name: 'chromium',
      provider: 'playwright',
      headless: true,
      screenshotOnFailure: true,
    },
  },
})
