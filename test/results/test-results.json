{"numTotalTestSuites": 182, "numPassedTestSuites": 182, "numFailedTestSuites": 0, "numPendingTestSuites": 0, "numTotalTests": 333, "numPassedTests": 333, "numFailedTests": 0, "numPendingTests": 0, "numTodoTests": 0, "snapshot": {"added": 0, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0, "didUpdate": false}, "startTime": 1752963621703, "success": true, "testResults": [{"assertionResults": [{"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "User Registration Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) User Registration Journey should complete full user registration flow", "status": "passed", "title": "should complete full user registration flow", "duration": 4.798036000000138, "failureMessages": [], "location": {"line": 90, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "User Registration Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) User Registration Journey should handle registration validation errors", "status": "passed", "title": "should handle registration validation errors", "duration": 1.13124500000049, "failureMessages": [], "location": {"line": 117, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "User Registration Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) User Registration Journey should handle existing email registration attempt", "status": "passed", "title": "should handle existing email registration attempt", "duration": 0.6676310000002559, "failureMessages": [], "location": {"line": 138, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "User Login <PERSON>"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) User Login Journey should complete successful login flow", "status": "passed", "title": "should complete successful login flow", "duration": 0.6738470000000234, "failureMessages": [], "location": {"line": 161, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "User Login <PERSON>"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) User Login Journey should handle invalid login credentials", "status": "passed", "title": "should handle invalid login credentials", "duration": 0.47909900000013295, "failureMessages": [], "location": {"line": 184, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "User Login <PERSON>"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) User Login Journey should redirect to intended page after login", "status": "passed", "title": "should redirect to intended page after login", "duration": 0.3322119999993447, "failureMessages": [], "location": {"line": 203, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "User Lo<PERSON>ut <PERSON>"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) User Logout Journey should complete logout flow", "status": "passed", "title": "should complete logout flow", "duration": 0.593310999998721, "failureMessages": [], "location": {"line": 223, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "Password Reset Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) Password Reset Journey should complete password reset request flow", "status": "passed", "title": "should complete password reset request flow", "duration": 0.43584700000064913, "failureMessages": [], "location": {"line": 246, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "Password Reset Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) Password Reset Journey should complete password reset confirmation flow", "status": "passed", "title": "should complete password reset confirmation flow", "duration": 0.6136260000002949, "failureMessages": [], "location": {"line": 262, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "Profile Management Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) Profile Management Journey should complete profile update flow", "status": "passed", "title": "should complete profile update flow", "duration": 0.4959190000008675, "failureMessages": [], "location": {"line": 285, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "Profile Management Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) Profile Management Journey should complete password change flow", "status": "passed", "title": "should complete password change flow", "duration": 0.3140879999991739, "failureMessages": [], "location": {"line": 303, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "Session Management Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) Session Management Journey should handle session timeout gracefully", "status": "passed", "title": "should handle session timeout gracefully", "duration": 0.27507499999956053, "failureMessages": [], "location": {"line": 324, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "Session Management Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) Session Management Journey should handle concurrent sessions", "status": "passed", "title": "should handle concurrent sessions", "duration": 0.22857899999871734, "failureMessages": [], "location": {"line": 347, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "Error <PERSON>"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) Error Handling Journey should handle network errors gracefully", "status": "passed", "title": "should handle network errors gracefully", "duration": 0.3923429999995278, "failureMessages": [], "location": {"line": 367, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "Error <PERSON>"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) Error Handling Journey should handle server errors gracefully", "status": "passed", "title": "should handle server errors gracefully", "duration": 0.5066249999999854, "failureMessages": [], "location": {"line": 388, "column": 7}, "meta": {}}], "startTime": 1752963631547, "endTime": 1752963631560.5066, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/e2e/auth-journeys.test.js"}, {"assertionResults": [{"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Complete Login Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Complete Login Flow should complete full login flow successfully", "status": "passed", "title": "should complete full login flow successfully", "duration": 9.599379000000226, "failureMessages": [], "location": {"line": 66, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Complete Login Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Complete Login Flow should handle login failure gracefully", "status": "passed", "title": "should handle login failure gracefully", "duration": 1.5597609999995257, "failureMessages": [], "location": {"line": 101, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Complete Login Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Complete Login Flow should handle network errors during login", "status": "passed", "title": "should handle network errors during login", "duration": 1.1481320000002597, "failureMessages": [], "location": {"line": 126, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Complete Registration Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Complete Registration Flow should complete full registration flow successfully", "status": "passed", "title": "should complete full registration flow successfully", "duration": 1.4611930000000939, "failureMessages": [], "location": {"line": 142, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Complete Registration Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Complete Registration Flow should handle registration validation errors", "status": "passed", "title": "should handle registration validation errors", "duration": 0.9581859999998414, "failureMessages": [], "location": {"line": 165, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Complete Logout Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Complete Logout Flow should complete full logout flow successfully", "status": "passed", "title": "should complete full logout flow successfully", "duration": 1.375955000000431, "failureMessages": [], "location": {"line": 187, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Complete Logout Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Complete Logout Flow should handle logout errors gracefully", "status": "passed", "title": "should handle logout errors gracefully", "duration": 0.8294720000003508, "failureMessages": [], "location": {"line": 212, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Password Reset Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Password Reset Flow should complete password reset request flow", "status": "passed", "title": "should complete password reset request flow", "duration": 0.9121809999996913, "failureMessages": [], "location": {"line": 232, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Password Reset Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Password Reset Flow should complete password reset confirmation flow", "status": "passed", "title": "should complete password reset confirmation flow", "duration": 16.7900810000001, "failureMessages": [], "location": {"line": 245, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Session Restoration Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Session Restoration Flow should restore session from localStorage on initialization", "status": "passed", "title": "should restore session from localStorage on initialization", "duration": 1.4197249999997439, "failureMessages": [], "location": {"line": 260, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Session Restoration Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Session Restoration Flow should handle invalid stored session gracefully", "status": "passed", "title": "should handle invalid stored session gracefully", "duration": 7.339694999999665, "failureMessages": [], "location": {"line": 288, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Profile Management Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Profile Management Flow should complete profile update flow", "status": "passed", "title": "should complete profile update flow", "duration": 1.1748370000004797, "failureMessages": [], "location": {"line": 310, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Profile Management Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Profile Management Flow should complete password change flow", "status": "passed", "title": "should complete password change flow", "duration": 0.8594789999997374, "failureMessages": [], "location": {"line": 331, "column": 7}, "meta": {}}], "startTime": 1752963627624, "endTime": 1752963627674.8594, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/integration/auth-flows.test.js"}, {"assertionResults": [{"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Network Error Handling"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Network Error Handling should handle network errors during login", "status": "passed", "title": "should handle network errors during login", "duration": 8.386067000000367, "failureMessages": [], "location": {"line": 101, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Network Error Handling"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Network Error Handling should handle timeout errors", "status": "passed", "title": "should handle timeout errors", "duration": 51.93643299999985, "failureMessages": [], "location": {"line": 118, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Network Error Handling"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Network Error Handling should handle server errors gracefully", "status": "passed", "title": "should handle server errors gracefully", "duration": 0.9373729999997522, "failureMessages": [], "location": {"line": 137, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "LocalStorage Error Handling"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) LocalStorage Error Handling should handle localStorage unavailability", "status": "passed", "title": "should handle localStorage unavailability", "duration": 3.287700999999288, "failureMessages": [], "location": {"line": 158, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "LocalStorage Error Handling"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) LocalStorage Error Handling should handle localStorage quota exceeded", "status": "passed", "title": "should handle localStorage quota exceeded", "duration": 1.1206189999993512, "failureMessages": [], "location": {"line": 175, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "LocalStorage Error Handling"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) LocalStorage Error Handling should handle corrupted localStorage data", "status": "passed", "title": "should handle corrupted localStorage data", "duration": 1.182480000000396, "failureMessages": [], "location": {"line": 191, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Authentication Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Authentication Edge Cases should handle simultaneous login attempts", "status": "passed", "title": "should handle simultaneous login attempts", "duration": 101.87573299999985, "failureMessages": [], "location": {"line": 212, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Authentication Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Authentication Edge Cases should handle login with empty credentials", "status": "passed", "title": "should handle login with empty credentials", "duration": 0.733632999999827, "failureMessages": [], "location": {"line": 243, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Authentication Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Authentication Edge Cases should handle login with null/undefined credentials", "status": "passed", "title": "should handle login with null/undefined credentials", "duration": 1.3591589999996359, "failureMessages": [], "location": {"line": 252, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Authentication Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Authentication Edge Cases should handle extremely long input values", "status": "passed", "title": "should handle extremely long input values", "duration": 102.02002999999968, "failureMessages": [], "location": {"line": 263, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Token Refresh Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Token Refresh Edge Cases should handle token refresh when already refreshing", "status": "passed", "title": "should handle token refresh when already refreshing", "duration": 201.77344800000083, "failureMessages": [], "location": {"line": 280, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Token Refresh Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Token Refresh Edge Cases should handle token refresh with invalid response", "status": "passed", "title": "should handle token refresh with invalid response", "duration": 0.6464679999999134, "failureMessages": [], "location": {"line": 315, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Session Management Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Session Management Edge Cases should handle session restoration with partial data", "status": "passed", "title": "should handle session restoration with partial data", "duration": 0.8939300000001822, "failureMessages": [], "location": {"line": 333, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Session Management Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Session Management Edge Cases should handle concurrent session modifications", "status": "passed", "title": "should handle concurrent session modifications", "duration": 0.6348440000001574, "failureMessages": [], "location": {"line": 352, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Memory and Performance Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Memory and Performance Edge Cases should handle memory pressure gracefully", "status": "passed", "title": "should handle memory pressure gracefully", "duration": 116.31826700000056, "failureMessages": [], "location": {"line": 379, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Memory and Performance Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Memory and Performance Edge Cases should handle rapid successive operations", "status": "passed", "title": "should handle rapid successive operations", "duration": 114.24258800000007, "failureMessages": [], "location": {"line": 394, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Browser Compatibility Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Browser Compatibility Edge Cases should handle missing browser APIs gracefully", "status": "passed", "title": "should handle missing browser APIs gracefully", "duration": 1.329413000000386, "failureMessages": [], "location": {"line": 415, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Browser Compatibility Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Browser Compatibility Edge Cases should handle disabled cookies", "status": "passed", "title": "should handle disabled cookies", "duration": 0.9930610000001252, "failureMessages": [], "location": {"line": 428, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Data Validation Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Data Validation Edge Cases should handle malformed server responses", "status": "passed", "title": "should handle malformed server responses", "duration": 0.6150729999999385, "failureMessages": [], "location": {"line": 445, "column": 7}, "meta": {}}, {"ancestorTitles": ["Error Handling and Edge Cases Tests (TEST-010)", "Data Validation Edge Cases"], "fullName": "Error Handling and Edge Cases Tests (TEST-010) Data Validation Edge Cases should handle unexpected data types", "status": "passed", "title": "should handle unexpected data types", "duration": 0.9904459999997925, "failureMessages": [], "location": {"line": 465, "column": 7}, "meta": {}}], "startTime": 1752963625138, "endTime": 1752963625849.9905, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/integration/error-handling.test.js"}, {"assertionResults": [{"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Role Checking"], "fullName": "Role-Based Access Control Tests (TEST-007) Role Checking should correctly identify admin users", "status": "passed", "title": "should correctly identify admin users", "duration": 2.5759450000005018, "failureMessages": [], "location": {"line": 107, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Role Checking"], "fullName": "Role-Based Access Control Tests (TEST-007) Role Checking should correctly identify regular users", "status": "passed", "title": "should correctly identify regular users", "duration": 0.78107700000146, "failureMessages": [], "location": {"line": 120, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Role Checking"], "fullName": "Role-Based Access Control Tests (TEST-007) Role Checking should handle multiple roles", "status": "passed", "title": "should handle multiple roles", "duration": 0.6043200000003708, "failureMessages": [], "location": {"line": 135, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Permission Checking"], "fullName": "Role-Based Access Control Tests (TEST-007) Permission Checking should check individual permissions correctly", "status": "passed", "title": "should check individual permissions correctly", "duration": 0.5422610000005079, "failureMessages": [], "location": {"line": 152, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Permission Checking"], "fullName": "Role-Based Access Control Tests (TEST-007) Permission Checking should handle admin users with all permissions", "status": "passed", "title": "should handle admin users with all permissions", "duration": 0.8932189999995899, "failureMessages": [], "location": {"line": 167, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Permission Checking"], "fullName": "Role-Based Access Control Tests (TEST-007) Permission Checking should handle users with no permissions", "status": "passed", "title": "should handle users with no permissions", "duration": 0.44794200000069395, "failureMessages": [], "location": {"line": 182, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Route Access Control"], "fullName": "Role-Based Access Control Tests (TEST-007) Route Access Control should allow admin access to admin routes", "status": "passed", "title": "should allow admin access to admin routes", "duration": 1.213372000000163, "failureMessages": [], "location": {"line": 197, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Route Access Control"], "fullName": "Role-Based Access Control Tests (TEST-007) Route Access Control should deny regular user access to admin routes", "status": "passed", "title": "should deny regular user access to admin routes", "duration": 15.697256999999809, "failureMessages": [], "location": {"line": 211, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Route Access Control"], "fullName": "Role-Based Access Control Tests (TEST-007) Route Access Control should enforce role-based route access", "status": "passed", "title": "should enforce role-based route access", "duration": 1.5243879999998171, "failureMessages": [], "location": {"line": 225, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Route Access Control"], "fullName": "Role-Based Access Control Tests (TEST-007) Route Access Control should deny access when user lacks required role", "status": "passed", "title": "should deny access when user lacks required role", "duration": 12.008358000000953, "failureMessages": [], "location": {"line": 239, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Route Access Control"], "fullName": "Role-Based Access Control Tests (TEST-007) Route Access Control should enforce permission-based route access", "status": "passed", "title": "should enforce permission-based route access", "duration": 0.4966179999992164, "failureMessages": [], "location": {"line": 253, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Route Access Control"], "fullName": "Role-Based Access Control Tests (TEST-007) Route Access Control should deny access when user lacks required permissions", "status": "passed", "title": "should deny access when user lacks required permissions", "duration": 0.36452599999938684, "failureMessages": [], "location": {"line": 271, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Route Access Control"], "fullName": "Role-Based Access Control Tests (TEST-007) Route Access Control should redirect unauthenticated users to login", "status": "passed", "title": "should redirect unauthenticated users to login", "duration": 0.3357879999985016, "failureMessages": [], "location": {"line": 289, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Complex Access Control Scenarios"], "fullName": "Role-Based Access Control Tests (TEST-007) Complex Access Control Scenarios should handle routes with multiple requirements", "status": "passed", "title": "should handle routes with multiple requirements", "duration": 0.37478700000065146, "failureMessages": [], "location": {"line": 302, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Complex Access Control Scenarios"], "fullName": "Role-Based Access Control Tests (TEST-007) Complex Access Control Scenarios should deny access if any requirement is not met", "status": "passed", "title": "should deny access if any requirement is not met", "duration": 1.3018709999996645, "failureMessages": [], "location": {"line": 324, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Dynamic Permission Checking"], "fullName": "Role-Based Access Control Tests (TEST-007) Dynamic Permission Checking should check resource-specific permissions", "status": "passed", "title": "should check resource-specific permissions", "duration": 0.6365260000002309, "failureMessages": [], "location": {"line": 348, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Dynamic Permission Checking"], "fullName": "Role-Based Access Control Tests (TEST-007) Dynamic Permission Checking should handle hierarchical permissions", "status": "passed", "title": "should handle hierarchical permissions", "duration": 0.3742629999997007, "failureMessages": [], "location": {"line": 363, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Role Inheritance"], "fullName": "Role-Based Access Control Tests (TEST-007) Role Inheritance should handle role inheritance correctly", "status": "passed", "title": "should handle role inheritance correctly", "duration": 0.3249809999997524, "failureMessages": [], "location": {"line": 382, "column": 7}, "meta": {}}], "startTime": 1752963630721, "endTime": 1752963630763.325, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/integration/rbac.test.js"}, {"assertionResults": [{"ancestorTitles": ["Token Refresh Mechanism Tests (TEST-006)", "Automatic Token Refresh"], "fullName": "Token Refresh Mechanism Tests (TEST-006) Automatic Token Refresh should refresh token automatically before expiration", "status": "passed", "title": "should refresh token automatically before expiration", "duration": 15.189495999999053, "failureMessages": [], "location": {"line": 55, "column": 7}, "meta": {}}, {"ancestorTitles": ["Token Refresh Mechanism Tests (TEST-006)", "Automatic Token Refresh"], "fullName": "Token Refresh Mechanism Tests (TEST-006) Automatic Token Refresh should handle token refresh failure by logging out", "status": "passed", "title": "should handle token refresh failure by logging out", "duration": 2.2190039999986766, "failureMessages": [], "location": {"line": 85, "column": 7}, "meta": {}}, {"ancestorTitles": ["Token Refresh Mechanism Tests (TEST-006)", "Automatic Token Refresh"], "fullName": "Token Refresh Mechanism Tests (TEST-006) Automatic Token Refresh should handle network errors during token refresh", "status": "passed", "title": "should handle network errors during token refresh", "duration": 0.9846430000015971, "failureMessages": [], "location": {"line": 112, "column": 7}, "meta": {}}, {"ancestorTitles": ["Token Refresh Mechanism Tests (TEST-006)", "Token Refresh Timing"], "fullName": "Token Refresh Mechanism Tests (TEST-006) Token Refresh Timing should schedule token refresh based on token expiration", "status": "passed", "title": "should schedule token refresh based on token expiration", "duration": 1.025286000000051, "failureMessages": [], "location": {"line": 131, "column": 7}, "meta": {}}, {"ancestorTitles": ["Token Refresh Mechanism Tests (TEST-006)", "Token Refresh Timing"], "fullName": "Token Refresh Mechanism Tests (TEST-006) Token Refresh Timing should refresh immediately if token is already expired", "status": "passed", "title": "should refresh immediately if token is already expired", "duration": 0.6534650000012334, "failureMessages": [], "location": {"line": 158, "column": 7}, "meta": {}}, {"ancestorTitles": ["Token Refresh Mechanism Tests (TEST-006)", "Token Refresh During API Calls"], "fullName": "Token Refresh Mechanism Tests (TEST-006) Token Refresh During API Calls should refresh token when API returns 401 Unauthorized", "status": "passed", "title": "should refresh token when API returns 401 Unauthorized", "duration": 1.1050670000004175, "failureMessages": [], "location": {"line": 184, "column": 7}, "meta": {}}, {"ancestorTitles": ["Token Refresh Mechanism Tests (TEST-006)", "Token Refresh During API Calls"], "fullName": "Token Refresh Mechanism Tests (TEST-006) Token Refresh During API Calls should logout user if token refresh fails during API call", "status": "passed", "title": "should logout user if token refresh fails during API call", "duration": 2.9018930000002, "failureMessages": [], "location": {"line": 251, "column": 7}, "meta": {}}, {"ancestorTitles": ["Token Refresh Mechanism Tests (TEST-006)", "Concurrent Token Refresh"], "fullName": "Token Refresh Mechanism Tests (TEST-006) Concurrent Token Refresh should handle multiple simultaneous refresh requests", "status": "passed", "title": "should handle multiple simultaneous refresh requests", "duration": 1.38309499999923, "failureMessages": [], "location": {"line": 303, "column": 7}, "meta": {}}, {"ancestorTitles": ["Token Refresh Mechanism Tests (TEST-006)", "Token Refresh State Management"], "fullName": "Token Refresh Mechanism Tests (TEST-006) Token Refresh State Management should set loading state during token refresh", "status": "passed", "title": "should set loading state during token refresh", "duration": 0.9796189999997296, "failureMessages": [], "location": {"line": 339, "column": 7}, "meta": {}}, {"ancestorTitles": ["Token Refresh Mechanism Tests (TEST-006)", "Token Refresh State Management"], "fullName": "Token Refresh Mechanism Tests (TEST-006) Token Refresh State Management should clear error state on successful token refresh", "status": "passed", "title": "should clear error state on successful token refresh", "duration": 30.724270000000615, "failureMessages": [], "location": {"line": 364, "column": 7}, "meta": {}}], "startTime": 1752963630471, "endTime": 1752963630529.7244, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/integration/token-refresh.test.js"}, {"assertionResults": [{"ancestorTitles": ["Authentication Composable (TEST-002)"], "fullName": "Authentication Composable (TEST-002) should be properly configured", "status": "passed", "title": "should be properly configured", "duration": 3.9561529999991762, "failureMessages": [], "location": {"line": 149, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "State Access"], "fullName": "Authentication Composable (TEST-002) State Access should provide access to authentication state", "status": "passed", "title": "should provide access to authentication state", "duration": 11.459285999999338, "failureMessages": [], "location": {"line": 154, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "State Access"], "fullName": "Authentication Composable (TEST-002) State Access should reflect changes in authentication state", "status": "passed", "title": "should reflect changes in authentication state", "duration": 1.283002000000124, "failureMessages": [], "location": {"line": 164, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Login Function"], "fullName": "Authentication Composable (TEST-002) Login Function should login and redirect on success", "status": "passed", "title": "should login and redirect on success", "duration": 2.2709389999999985, "failureMessages": [], "location": {"line": 178, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Login Function"], "fullName": "Authentication Composable (TEST-002) Login Function should not redirect on login failure", "status": "passed", "title": "should not redirect on login failure", "duration": 0.890772000000652, "failureMessages": [], "location": {"line": 191, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Register Function"], "fullName": "Authentication Composable (TEST-002) Register Function should register and redirect to login on success", "status": "passed", "title": "should register and redirect to login on success", "duration": 0.6915260000005219, "failureMessages": [], "location": {"line": 206, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Register Function"], "fullName": "Authentication Composable (TEST-002) Register Function should not redirect on registration failure", "status": "passed", "title": "should not redirect on registration failure", "duration": 0.4320050000005722, "failureMessages": [], "location": {"line": 219, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Logout Function"], "fullName": "Authentication Composable (TEST-002) Logout Function should logout and redirect to login", "status": "passed", "title": "should logout and redirect to login", "duration": 0.5649869999997463, "failureMessages": [], "location": {"line": 233, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Token Refresh Function"], "fullName": "Authentication Composable (TEST-002) Token Refresh Function should refresh token", "status": "passed", "title": "should refresh token", "duration": 5.413285000000542, "failureMessages": [], "location": {"line": 246, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Password Reset Functions"], "fullName": "Authentication Composable (TEST-002) Password Reset Functions should request password reset", "status": "passed", "title": "should request password reset", "duration": 0.9736840000005031, "failureMessages": [], "location": {"line": 259, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Password Reset Functions"], "fullName": "Authentication Composable (TEST-002) Password Reset Functions should reset password and redirect to login on success", "status": "passed", "title": "should reset password and redirect to login on success", "duration": 1.426462999999785, "failureMessages": [], "location": {"line": 271, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Profile Management Functions"], "fullName": "Authentication Composable (TEST-002) Profile Management Functions should update profile", "status": "passed", "title": "should update profile", "duration": 0.4310079999995651, "failureMessages": [], "location": {"line": 286, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Profile Management Functions"], "fullName": "Authentication Composable (TEST-002) Profile Management Functions should change password", "status": "passed", "title": "should change password", "duration": 0.351224000000002, "failureMessages": [], "location": {"line": 298, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Route Guards"], "fullName": "Authentication Composable (TEST-002) Route Guards should allow access when authenticated (requireAuth)", "status": "passed", "title": "should allow access when authenticated (requireAuth)", "duration": 0.2554810000001453, "failureMessages": [], "location": {"line": 312, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Route Guards"], "fullName": "Authentication Composable (TEST-002) Route Guards should redirect to login when not authenticated (requireAuth)", "status": "passed", "title": "should redirect to login when not authenticated (requireAuth)", "duration": 1.7390400000003865, "failureMessages": [], "location": {"line": 322, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Route Guards"], "fullName": "Authentication Composable (TEST-002) Route Guards should allow access when not authenticated (requireGuest)", "status": "passed", "title": "should allow access when not authenticated (requireGuest)", "duration": 0.2965989999993326, "failureMessages": [], "location": {"line": 332, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Route Guards"], "fullName": "Authentication Composable (TEST-002) Route Guards should redirect to home when authenticated (requireGuest)", "status": "passed", "title": "should redirect to home when authenticated (requireGuest)", "duration": 2.4655130000010104, "failureMessages": [], "location": {"line": 342, "column": 7}, "meta": {}}], "startTime": 1752963628965, "endTime": 1752963629004.4656, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/composables/useAuth.test.js"}, {"assertionResults": [{"ancestorTitles": ["useSessionManagement", "initialization"], "fullName": "useSessionManagement initialization should initialize with empty sessions", "status": "passed", "title": "should initialize with empty sessions", "duration": 6.812218999999459, "failureMessages": [], "location": {"line": 63, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "fetchSessions"], "fullName": "useSessionManagement fetchSessions should fetch sessions successfully", "status": "passed", "title": "should fetch sessions successfully", "duration": 4.905107999999927, "failureMessages": [], "location": {"line": 81, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "fetchSessions"], "fullName": "useSessionManagement fetchSessions should handle fetch errors", "status": "passed", "title": "should handle fetch errors", "duration": 4.38603299999977, "failureMessages": [], "location": {"line": 142, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "fetchSessions"], "fullName": "useSessionManagement fetchSessions should handle API errors", "status": "passed", "title": "should handle API errors", "duration": 5.628888000000188, "failureMessages": [], "location": {"line": 163, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "fetchSessions"], "fullName": "useSessionManagement fetchSessions should handle API response errors", "status": "passed", "title": "should handle API response errors", "duration": 0.8438139999998384, "failureMessages": [], "location": {"line": 186, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "fetchSessions"], "fullName": "useSessionManagement fetchSessions should not fetch when unauthenticated", "status": "passed", "title": "should not fetch when unauthenticated", "duration": 0.6714180000008128, "failureMessages": [], "location": {"line": 211, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "terminateSession"], "fullName": "useSessionManagement terminateSession should terminate session successfully", "status": "passed", "title": "should terminate session successfully", "duration": 1.8695590000006632, "failureMessages": [], "location": {"line": 225, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "terminateSession"], "fullName": "useSessionManagement terminateSession should handle termination errors", "status": "passed", "title": "should handle termination errors", "duration": 2.8815250000006927, "failureMessages": [], "location": {"line": 289, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "terminateSession"], "fullName": "useSessionManagement terminateSession should handle API termination errors", "status": "passed", "title": "should handle API termination errors", "duration": 2.0501100000001315, "failureMessages": [], "location": {"line": 306, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "terminateAllOtherSessions"], "fullName": "useSessionManagement terminateAllOtherSessions should terminate all other sessions successfully", "status": "passed", "title": "should terminate all other sessions successfully", "duration": 1.0407460000005813, "failureMessages": [], "location": {"line": 329, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "terminateAllOtherSessions"], "fullName": "useSessionManagement terminateAllOtherSessions should handle termination errors", "status": "passed", "title": "should handle termination errors", "duration": 0.739582000000155, "failureMessages": [], "location": {"line": 381, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "refreshCurrentSession"], "fullName": "useSessionManagement refreshCurrentSession should refresh current session successfully", "status": "passed", "title": "should refresh current session successfully", "duration": 1.2261639999996987, "failureMessages": [], "location": {"line": 400, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "refreshCurrentSession"], "fullName": "useSessionManagement refreshCurrentSession should handle refresh errors", "status": "passed", "title": "should handle refresh errors", "duration": 0.7065640000000712, "failureMessages": [], "location": {"line": 431, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "computed properties"], "fullName": "useSessionManagement computed properties should compute active sessions correctly", "status": "passed", "title": "should compute active sessions correctly", "duration": 0.9699460000001636, "failureMessages": [], "location": {"line": 450, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "computed properties"], "fullName": "useSessionManagement computed properties should identify current session correctly", "status": "passed", "title": "should identify current session correctly", "duration": 1.0578590000004624, "failureMessages": [], "location": {"line": 482, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "computed properties"], "fullName": "useSessionManagement computed properties should compute other sessions correctly", "status": "passed", "title": "should compute other sessions correctly", "duration": 1.3591299999998228, "failureMessages": [], "location": {"line": 515, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "computed properties"], "fullName": "useSessionManagement computed properties should compute session statistics correctly", "status": "passed", "title": "should compute session statistics correctly", "duration": 1.2474069999998392, "failureMessages": [], "location": {"line": 548, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "utility functions"], "fullName": "useSessionManagement utility functions should detect device type correctly", "status": "passed", "title": "should detect device type correctly", "duration": 0.8596210000005158, "failureMessages": [], "location": {"line": 587, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "utility functions"], "fullName": "useSessionManagement utility functions should detect browser name correctly", "status": "passed", "title": "should detect browser name correctly", "duration": 0.676101999999446, "failureMessages": [], "location": {"line": 604, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "CSRF token handling"], "fullName": "useSessionManagement CSRF token handling should get CSRF token successfully", "status": "passed", "title": "should get CSRF token successfully", "duration": 0.8063040000006367, "failureMessages": [], "location": {"line": 623, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "CSRF token handling"], "fullName": "useSessionManagement CSRF token handling should handle CSRF token errors gracefully", "status": "passed", "title": "should handle CSRF token errors gracefully", "duration": 0.6040350000002945, "failureMessages": [], "location": {"line": 652, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "auto refresh"], "fullName": "useSessionManagement auto refresh should start auto refresh", "status": "passed", "title": "should start auto refresh", "duration": 0.8825450000003912, "failureMessages": [], "location": {"line": 673, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "auto refresh"], "fullName": "useSessionManagement auto refresh should stop auto refresh", "status": "passed", "title": "should stop auto refresh", "duration": 0.790606999999909, "failureMessages": [], "location": {"line": 682, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "auto refresh"], "fullName": "useSessionManagement auto refresh should refresh sessions automatically", "status": "passed", "title": "should refresh sessions automatically", "duration": 1.2167159999999058, "failureMessages": [], "location": {"line": 693, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "error handling"], "fullName": "useSessionManagement error handling should clear errors", "status": "passed", "title": "should clear errors", "duration": 0.7665080000006128, "failureMessages": [], "location": {"line": 714, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "new session detection"], "fullName": "useSessionManagement new session detection should detect new sessions", "status": "passed", "title": "should detect new sessions", "duration": 0.8429940000005445, "failureMessages": [], "location": {"line": 737, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "new session detection"], "fullName": "useSessionManagement new session detection should handle no new sessions", "status": "passed", "title": "should handle no new sessions", "duration": 0.7878630000004705, "failureMessages": [], "location": {"line": 789, "column": 7}, "meta": {}}], "startTime": 1752963625173, "endTime": 1752963625220.843, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js"}, {"assertionResults": [{"ancestorTitles": ["useSessionTimeout", "initialization"], "fullName": "useSessionTimeout initialization should initialize with default configuration", "status": "passed", "title": "should initialize with default configuration", "duration": 6.911880999999994, "failureMessages": [], "location": {"line": 97, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "initialization"], "fullName": "useSessionTimeout initialization should accept custom configuration", "status": "passed", "title": "should accept custom configuration", "duration": 2.1168970000007903, "failureMessages": [], "location": {"line": 113, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "activity tracking"], "fullName": "useSessionTimeout activity tracking should register activity event listeners", "status": "passed", "title": "should register activity event listeners", "duration": 2.918721000000005, "failureMessages": [], "location": {"line": 128, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "activity tracking"], "fullName": "useSessionTimeout activity tracking should update activity timestamp on user activity", "status": "passed", "title": "should update activity timestamp on user activity", "duration": 4.116933000000245, "failureMessages": [], "location": {"line": 141, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "activity tracking"], "fullName": "useSessionTimeout activity tracking should handle localStorage errors gracefully", "status": "passed", "title": "should handle localStorage errors gracefully", "duration": 2.187992000000122, "failureMessages": [], "location": {"line": 158, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "warning system"], "fullName": "useSessionTimeout warning system should show warning when approaching timeout", "status": "passed", "title": "should show warning when approaching timeout", "duration": 1.5944530000006125, "failureMessages": [], "location": {"line": 170, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "warning system"], "fullName": "useSessionTimeout warning system should hide warning when activity is detected", "status": "passed", "title": "should hide warning when activity is detected", "duration": 1.1975759999995716, "failureMessages": [], "location": {"line": 186, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "warning system"], "fullName": "useSessionTimeout warning system should format time remaining correctly", "status": "passed", "title": "should format time remaining correctly", "duration": 0.9526139999998122, "failureMessages": [], "location": {"line": 206, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "session extension"], "fullName": "useSessionTimeout session extension should extend session successfully", "status": "passed", "title": "should extend session successfully", "duration": 2.3158469999998488, "failureMessages": [], "location": {"line": 226, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "session extension"], "fullName": "useSessionTimeout session extension should handle session extension failure", "status": "passed", "title": "should handle session extension failure", "duration": 2.6138389999996434, "failureMessages": [], "location": {"line": 246, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "session extension"], "fullName": "useSessionTimeout session extension should handle network errors during extension", "status": "passed", "title": "should handle network errors during extension", "duration": 1.5371560000003228, "failureMessages": [], "location": {"line": 261, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "session timeout handling"], "fullName": "useSessionTimeout session timeout handling should logout user when session times out", "status": "passed", "title": "should logout user when session times out", "duration": 0.6920030000001134, "failureMessages": [], "location": {"line": 274, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "session timeout handling"], "fullName": "useSessionTimeout session timeout handling should redirect to login even if logout fails", "status": "passed", "title": "should redirect to login even if logout fails", "duration": 0.6793649999999616, "failureMessages": [], "location": {"line": 284, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "cross-tab communication"], "fullName": "useSessionTimeout cross-tab communication should check for activity in other tabs", "status": "passed", "title": "should check for activity in other tabs", "duration": 1.0538739999992686, "failureMessages": [], "location": {"line": 296, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "cross-tab communication"], "fullName": "useSessionTimeout cross-tab communication should handle localStorage errors during cross-tab check", "status": "passed", "title": "should handle localStorage errors during cross-tab check", "duration": 2.342202000000725, "failureMessages": [], "location": {"line": 310, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "cleanup"], "fullName": "useSessionTimeout cleanup should remove event listeners when stopped", "status": "passed", "title": "should remove event listeners when stopped", "duration": 0.8969180000003689, "failureMessages": [], "location": {"line": 322, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "cleanup"], "fullName": "useSessionTimeout cleanup should clear timers when stopped", "status": "passed", "title": "should clear timers when stopped", "duration": 0.8900389999998879, "failureMessages": [], "location": {"line": 333, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "session status"], "fullName": "useSessionTimeout session status should provide comprehensive session status", "status": "passed", "title": "should provide comprehensive session status", "duration": 1.271719000000303, "failureMessages": [], "location": {"line": 345, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "edge cases"], "fullName": "useSessionTimeout edge cases should handle unauthenticated state gracefully", "status": "passed", "title": "should handle unauthenticated state gracefully", "duration": 1.2999689999996917, "failureMessages": [], "location": {"line": 370, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "edge cases"], "fullName": "useSessionTimeout edge cases should handle multiple start/stop cycles", "status": "passed", "title": "should handle multiple start/stop cycles", "duration": 1.0369520000003831, "failureMessages": [], "location": {"line": 382, "column": 7}, "meta": {}}], "startTime": 1752963625140, "endTime": 1752963625181.0369, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js"}, {"assertionResults": [{"ancestorTitles": ["Route Guards (TEST-003)", "Authentication Guard"], "fullName": "Route Guards (TEST-003) Authentication Guard should initialize auth store if not initialized", "status": "passed", "title": "should initialize auth store if not initialized", "duration": 3.3671529999992345, "failureMessages": [], "location": {"line": 106, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Authentication Guard"], "fullName": "Route Guards (TEST-003) Authentication Guard should not initialize auth store if already initialized", "status": "passed", "title": "should not initialize auth store if already initialized", "duration": 0.6386749999983294, "failureMessages": [], "location": {"line": 120, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Protected Routes"], "fullName": "Route Guards (TEST-003) Protected Routes should allow access to protected routes when authenticated", "status": "passed", "title": "should allow access to protected routes when authenticated", "duration": 1.234941999999137, "failureMessages": [], "location": {"line": 135, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Protected Routes"], "fullName": "Route Guards (TEST-003) Protected Routes should redirect to login when accessing protected routes while unauthenticated", "status": "passed", "title": "should redirect to login when accessing protected routes while unauthenticated", "duration": 3.2563279999994847, "failureMessages": [], "location": {"line": 148, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Protected Routes"], "fullName": "Route Guards (TEST-003) Protected Routes should preserve query parameters in redirect", "status": "passed", "title": "should preserve query parameters in redirect", "duration": 0.736044999999649, "failureMessages": [], "location": {"line": 164, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Guest-Only Routes"], "fullName": "Route Guards (TEST-003) Guest-Only Routes should allow access to guest-only routes when unauthenticated", "status": "passed", "title": "should allow access to guest-only routes when unauthenticated", "duration": 15.218174999999974, "failureMessages": [], "location": {"line": 183, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Guest-Only Routes"], "fullName": "Route Guards (TEST-003) Guest-Only Routes should redirect to home when accessing guest-only routes while authenticated", "status": "passed", "title": "should redirect to home when accessing guest-only routes while authenticated", "duration": 0.5994719999998779, "failureMessages": [], "location": {"line": 196, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Guest-Only Routes"], "fullName": "Route Guards (TEST-003) Guest-Only Routes should redirect authenticated users from register page", "status": "passed", "title": "should redirect authenticated users from register page", "duration": 0.6940430000013293, "failureMessages": [], "location": {"line": 209, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Guest-Only Routes"], "fullName": "Route Guards (TEST-003) Guest-Only Routes should redirect authenticated users from forgot-password page", "status": "passed", "title": "should redirect authenticated users from forgot-password page", "duration": 1.1598780000003899, "failureMessages": [], "location": {"line": 222, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Admin Routes"], "fullName": "Route Guards (TEST-003) Admin Routes should allow access to admin routes when user is admin", "status": "passed", "title": "should allow access to admin routes when user is admin", "duration": 0.5244710000006307, "failureMessages": [], "location": {"line": 237, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Admin Routes"], "fullName": "Route Guards (TEST-003) Admin Routes should redirect to unauthorized when non-admin user accesses admin routes", "status": "passed", "title": "should redirect to unauthorized when non-admin user accesses admin routes", "duration": 0.5700200000010227, "failureMessages": [], "location": {"line": 251, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Admin Routes"], "fullName": "Route Guards (TEST-003) Admin Routes should redirect to login when unauthenticated user accesses admin routes", "status": "passed", "title": "should redirect to login when unauthenticated user accesses admin routes", "duration": 1.0865080000003218, "failureMessages": [], "location": {"line": 265, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Public Routes"], "fullName": "Route Guards (TEST-003) Public Routes should allow access to public routes regardless of authentication status", "status": "passed", "title": "should allow access to public routes regardless of authentication status", "duration": 0.3632109999998647, "failureMessages": [], "location": {"line": 284, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Public Routes"], "fullName": "Route Guards (TEST-003) Public Routes should allow authenticated users to access public routes", "status": "passed", "title": "should allow authenticated users to access public routes", "duration": 0.2944569999999658, "failureMessages": [], "location": {"line": 297, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Erro<PERSON>"], "fullName": "Route Guards (TEST-003) Error Handling should handle auth initialization errors gracefully", "status": "passed", "title": "should handle auth initialization errors gracefully", "duration": 0.6398239999998623, "failureMessages": [], "location": {"line": 312, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Route Meta Combinations"], "fullName": "Route Guards (TEST-003) Route Meta Combinations should handle routes with multiple meta requirements", "status": "passed", "title": "should handle routes with multiple meta requirements", "duration": 0.3390049999998155, "failureMessages": [], "location": {"line": 329, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Route Meta Combinations"], "fullName": "Route Guards (TEST-003) Route Meta Combinations should prioritize authentication check over admin check", "status": "passed", "title": "should prioritize authentication check over admin check", "duration": 0.4144049999995332, "failureMessages": [], "location": {"line": 346, "column": 7}, "meta": {}}], "startTime": 1752963630708, "endTime": 1752963630740.4143, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/router/guards.test.js"}, {"assertionResults": [{"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Login API"], "fullName": "Authentication API Middleware (TEST-004) Login API should call PocketBase login with correct parameters", "status": "passed", "title": "should call PocketBase login with correct parameters", "duration": 7.067236999999295, "failureMessages": [], "location": {"line": 73, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Login API"], "fullName": "Authentication API Middleware (TEST-004) Login API should handle login API errors", "status": "passed", "title": "should handle login API errors", "duration": 1.8147750000007363, "failureMessages": [], "location": {"line": 88, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Login API"], "fullName": "Authentication API Middleware (TEST-004) Login API should validate input parameters", "status": "passed", "title": "should validate input parameters", "duration": 0.6700220000002446, "failureMessages": [], "location": {"line": 95, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Registration API"], "fullName": "Authentication API Middleware (TEST-004) Registration API should call PocketBase register with correct parameters", "status": "passed", "title": "should call PocketBase register with correct parameters", "duration": 0.9971740000000864, "failureMessages": [], "location": {"line": 105, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Registration API"], "fullName": "Authentication API Middleware (TEST-004) Registration API should handle registration API errors", "status": "passed", "title": "should handle registration API errors", "duration": 0.4921889999995983, "failureMessages": [], "location": {"line": 124, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Registration API"], "fullName": "Authentication API Middleware (TEST-004) Registration API should validate registration data", "status": "passed", "title": "should validate registration data", "duration": 0.5458770000004733, "failureMessages": [], "location": {"line": 132, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Token Refresh API"], "fullName": "Authentication API Middleware (TEST-004) Token Refresh API should call PocketBase refreshToken", "status": "passed", "title": "should call PocketBase refreshToken", "duration": 0.6271420000002763, "failureMessages": [], "location": {"line": 140, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Token Refresh API"], "fullName": "Authentication API Middleware (TEST-004) Token Refresh API should handle token refresh API errors", "status": "passed", "title": "should handle token refresh API errors", "duration": 0.3753660000002128, "failureMessages": [], "location": {"line": 155, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Token Refresh API"], "fullName": "Authentication API Middleware (TEST-004) Token Refresh API should handle expired refresh tokens", "status": "passed", "title": "should handle expired refresh tokens", "duration": 0.7638489999999365, "failureMessages": [], "location": {"line": 162, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Password Reset API"], "fullName": "Authentication API Middleware (TEST-004) Password Reset API should call PocketBase requestPasswordReset", "status": "passed", "title": "should call PocketBase requestPasswordReset", "duration": 0.6218010000002323, "failureMessages": [], "location": {"line": 174, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Password Reset API"], "fullName": "Authentication API Middleware (TEST-004) Password Reset API should call PocketBase resetPassword", "status": "passed", "title": "should call PocketBase resetPassword", "duration": 0.5903140000000349, "failureMessages": [], "location": {"line": 184, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Profile Management API"], "fullName": "Authentication API Middleware (TEST-004) Profile Management API should call PocketBase updateProfile", "status": "passed", "title": "should call PocketBase updateProfile", "duration": 0.5210449999995035, "failureMessages": [], "location": {"line": 199, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Profile Management API"], "fullName": "Authentication API Middleware (TEST-004) Profile Management API should call PocketBase changePassword", "status": "passed", "title": "should call PocketBase changePassword", "duration": 0.3690599999999904, "failureMessages": [], "location": {"line": 213, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Authentication Status API"], "fullName": "Authentication API Middleware (TEST-004) Authentication Status API should call PocketBase isAuthenticated", "status": "passed", "title": "should call PocketBase isAuthenticated", "duration": 4.2537359999996625, "failureMessages": [], "location": {"line": 228, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Authentication Status API"], "fullName": "Authentication API Middleware (TEST-004) Authentication Status API should call PocketBase getCurrentUser", "status": "passed", "title": "should call PocketBase getCurrentUser", "duration": 0.4116759999997157, "failureMessages": [], "location": {"line": 237, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Authentication Status API"], "fullName": "Authentication API Middleware (TEST-004) Authentication Status API should call PocketBase getToken", "status": "passed", "title": "should call PocketBase getToken", "duration": 0.34727699999984907, "failureMessages": [], "location": {"line": 250, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Authentication Status API"], "fullName": "Authentication API Middleware (TEST-004) Authentication Status API should handle authentication check errors", "status": "passed", "title": "should handle authentication check errors", "duration": 0.3417589999999109, "failureMessages": [], "location": {"line": 260, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Logout API"], "fullName": "Authentication API Middleware (TEST-004) Logout API should call PocketBase logout", "status": "passed", "title": "should call PocketBase logout", "duration": 0.2857509999994363, "failureMessages": [], "location": {"line": 268, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Logout API"], "fullName": "Authentication API Middleware (TEST-004) Logout API should handle logout API errors", "status": "passed", "title": "should handle logout API errors", "duration": 0.3021889999999985, "failureMessages": [], "location": {"line": 278, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "Logout API"], "fullName": "Authentication API Middleware (TEST-004) Logout API should handle logout when not authenticated", "status": "passed", "title": "should handle logout when not authenticated", "duration": 0.18492299999979878, "failureMessages": [], "location": {"line": 285, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "API Response Handling"], "fullName": "Authentication API Middleware (TEST-004) API Response Handling should handle network timeouts", "status": "passed", "title": "should handle network timeouts", "duration": 101.07444799999939, "failureMessages": [], "location": {"line": 298, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication API Middleware (TEST-004)", "API Response Handling"], "fullName": "Authentication API Middleware (TEST-004) API Response Handling should handle rate limiting", "status": "passed", "title": "should handle rate limiting", "duration": 0.4917429999995875, "failureMessages": [], "location": {"line": 308, "column": 7}, "meta": {}}], "startTime": 1752963625092, "endTime": 1752963625216.4917, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/middleware/auth-middleware.test.js"}, {"assertionResults": [{"ancestorTitles": ["CSRF Middleware", "generateCSRFToken"], "fullName": "CSRF Middleware generateCSRFToken should generate and set CSRF token in cookie and header", "status": "passed", "title": "should generate and set CSRF token in cookie and header", "duration": 30.40718199999992, "failureMessages": [], "location": {"line": 38, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "generateCSRFToken"], "fullName": "CSRF Middleware generateCSRFToken should set secure cookie in production", "status": "passed", "title": "should set secure cookie in production", "duration": 4.869768999999906, "failureMessages": [], "location": {"line": 53, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "validateCSRFToken"], "fullName": "CSRF Middleware validateCSRFToken should skip validation for GET requests", "status": "passed", "title": "should skip validation for GET requests", "duration": 3.8496539999996457, "failureMessages": [], "location": {"line": 70, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "validateCSRFToken"], "fullName": "CSRF Middleware validateCSRFToken should skip validation for HEAD requests", "status": "passed", "title": "should skip validation for HEAD requests", "duration": 3.240074999999706, "failureMessages": [], "location": {"line": 79, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "validateCSRFToken"], "fullName": "CSRF Middleware validateCSRFToken should skip validation for OPTIONS requests", "status": "passed", "title": "should skip validation for OPTIONS requests", "duration": 4.309263000000101, "failureMessages": [], "location": {"line": 88, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "csrfProtection"], "fullName": "CSRF Middleware csrfProtection should protect authentication endpoints", "status": "passed", "title": "should protect authentication endpoints", "duration": 8.154343999999583, "failureMessages": [], "location": {"line": 105, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "csrfProtection"], "fullName": "CSRF Middleware csrfProtection should protect registration endpoints", "status": "passed", "title": "should protect registration endpoints", "duration": 3.8184170000004087, "failureMessages": [], "location": {"line": 114, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "csrfProtection"], "fullName": "CSRF Middleware csrfProtection should not protect non-authentication endpoints", "status": "passed", "title": "should not protect non-authentication endpoints", "duration": 3.3962689999998474, "failureMessages": [], "location": {"line": 123, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "csrfProtection"], "fullName": "CSRF Middleware csrfProtection should not protect GET requests to auth endpoints", "status": "passed", "title": "should not protect GET requests to auth endpoints", "duration": 5.78491699999995, "failureMessages": [], "location": {"line": 132, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "getCSRFToken"], "fullName": "CSRF Middleware getCSRFToken should return null for non-existent session", "status": "passed", "title": "should return null for non-existent session", "duration": 1.444257000000107, "failureMessages": [], "location": {"line": 143, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "cleanupExpiredTokens"], "fullName": "CSRF Middleware cleanupExpiredTokens should clean up expired tokens", "status": "passed", "title": "should clean up expired tokens", "duration": 1.3813819999995758, "failureMessages": [], "location": {"line": 151, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "CSRF token endpoint"], "fullName": "CSRF Middleware CSRF token endpoint should provide CSRF token via API endpoint", "status": "passed", "title": "should provide CSRF token via API endpoint", "duration": 3.924602999999479, "failureMessages": [], "location": {"line": 159, "column": 5}, "meta": {}}], "startTime": 1752963627009, "endTime": 1752963627083.9246, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/middleware/csrf.test.js"}, {"assertionResults": [{"ancestorTitles": ["Password Reset Middleware", "storeResetToken"], "fullName": "Password Reset Middleware storeResetToken should store reset token with expiration", "status": "passed", "title": "should store reset token with expiration", "duration": 4.799101000000519, "failureMessages": [], "location": {"line": 39, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "storeResetToken"], "fullName": "Password Reset Middleware storeResetToken should use default expiration time", "status": "passed", "title": "should use default expiration time", "duration": 1.7634859999998298, "failureMessages": [], "location": {"line": 55, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetToken"], "fullName": "Password Reset Middleware validateResetToken should validate valid token", "status": "passed", "title": "should validate valid token", "duration": 1.4178019999999378, "failureMessages": [], "location": {"line": 66, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetToken"], "fullName": "Password Reset Middleware validateResetToken should reject non-existent token", "status": "passed", "title": "should reject non-existent token", "duration": 0.5333399999999529, "failureMessages": [], "location": {"line": 78, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetToken"], "fullName": "Password Reset Middleware validateResetToken should reject expired token", "status": "passed", "title": "should reject expired token", "duration": 0.4316880000005767, "failureMessages": [], "location": {"line": 85, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetToken"], "fullName": "Password Reset Middleware validateResetToken should reject used token", "status": "passed", "title": "should reject used token", "duration": 0.613734999999906, "failureMessages": [], "location": {"line": 100, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetToken"], "fullName": "Password Reset Middleware validateResetToken should increment attempt counter", "status": "passed", "title": "should increment attempt counter", "duration": 0.6121290000000954, "failureMessages": [], "location": {"line": 113, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetToken"], "fullName": "Password Reset Middleware validateResetToken should reject token after too many attempts", "status": "passed", "title": "should reject token after too many attempts", "duration": 0.7798960000000079, "failureMessages": [], "location": {"line": 126, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "markTokenAsUsed"], "fullName": "Password Reset Middleware markTokenAsUsed should mark token as used", "status": "passed", "title": "should mark token as used", "duration": 0.9877529999994294, "failureMessages": [], "location": {"line": 146, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "markTokenAsUsed"], "fullName": "Password Reset Middleware markTokenAsUsed should handle non-existent token gracefully", "status": "passed", "title": "should handle non-existent token gracefully", "duration": 1.1998559999992722, "failureMessages": [], "location": {"line": 157, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "invalidateEmailResetTokens"], "fullName": "Password Reset Middleware invalidateEmailResetTokens should invalidate existing tokens for email", "status": "passed", "title": "should invalidate existing tokens for email", "duration": 0.5232029999997394, "failureMessages": [], "location": {"line": 163, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "passwordResetRateLimit"], "fullName": "Password Reset Middleware passwordResetRateLimit should allow requests within rate limit", "status": "passed", "title": "should allow requests within rate limit", "duration": 100.36976199999935, "failureMessages": [], "location": {"line": 185, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "passwordResetRateLimit"], "fullName": "Password Reset Middleware passwordResetRateLimit should block requests exceeding rate limit", "status": "passed", "title": "should block requests exceeding rate limit", "duration": 21.946748999999727, "failureMessages": [], "location": {"line": 198, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "emailResetRateLimit"], "fullName": "Password Reset Middleware emailResetRateLimit should allow requests for different emails", "status": "passed", "title": "should allow requests for different emails", "duration": 8.158088999999563, "failureMessages": [], "location": {"line": 228, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "emailResetRateLimit"], "fullName": "Password Reset Middleware emailResetRateLimit should block excessive requests for same email", "status": "passed", "title": "should block excessive requests for same email", "duration": 27.490923000000294, "failureMessages": [], "location": {"line": 246, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetTokenMiddleware"], "fullName": "Password Reset Middleware validateResetTokenMiddleware should validate token and attach data to request", "status": "passed", "title": "should validate token and attach data to request", "duration": 5.3652189999993425, "failureMessages": [], "location": {"line": 277, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetTokenMiddleware"], "fullName": "Password Reset Middleware validateResetTokenMiddleware should reject request with invalid token", "status": "passed", "title": "should reject request with invalid token", "duration": 13.044794999999795, "failureMessages": [], "location": {"line": 301, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetTokenMiddleware"], "fullName": "Password Reset Middleware validateResetTokenMiddleware should reject request without token", "status": "passed", "title": "should reject request without token", "duration": 4.558402000000569, "failureMessages": [], "location": {"line": 313, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "progressiveDelayMiddleware"], "fullName": "Password Reset Middleware progressiveDelayMiddleware should not delay first request", "status": "passed", "title": "should not delay first request", "duration": 3.2555890000003274, "failureMessages": [], "location": {"line": 327, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "progressiveDelayMiddleware"], "fullName": "Password Reset Middleware progressiveDelayMiddleware should handle missing email gracefully", "status": "passed", "title": "should handle missing email gracefully", "duration": 2.3736930000004577, "failureMessages": [], "location": {"line": 342, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "cleanupExpiredData"], "fullName": "Password Reset Middleware cleanupExpiredData should clean up expired tokens", "status": "passed", "title": "should clean up expired tokens", "duration": 0.41976900000008754, "failureMessages": [], "location": {"line": 356, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "cleanupExpiredData"], "fullName": "Password Reset Middleware cleanupExpiredData should not throw errors during cleanup", "status": "passed", "title": "should not throw errors during cleanup", "duration": 1.4502910000001066, "failureMessages": [], "location": {"line": 373, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "getResetTokenStats"], "fullName": "Password Reset Middleware getResetTokenStats should return correct statistics", "status": "passed", "title": "should return correct statistics", "duration": 0.9523659999995289, "failureMessages": [], "location": {"line": 379, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "Integration tests"], "fullName": "Password Reset Middleware Integration tests should handle complete password reset flow", "status": "passed", "title": "should handle complete password reset flow", "duration": 0.509806999999455, "failureMessages": [], "location": {"line": 404, "column": 5}, "meta": {}}], "startTime": 1752963627017, "endTime": 1752963627222.5098, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/middleware/passwordReset.test.js"}, {"assertionResults": [{"ancestorTitles": ["Authentication Routes", "POST /api/auth/login"], "fullName": "Authentication Routes POST /api/auth/login should login user with valid credentials", "status": "passed", "title": "should login user with valid credentials", "duration": 113.01395400000001, "failureMessages": [], "location": {"line": 201, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/login"], "fullName": "Authentication Routes POST /api/auth/login should return validation error for invalid email", "status": "passed", "title": "should return validation error for invalid email", "duration": 6.352006000000074, "failureMessages": [], "location": {"line": 232, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/login"], "fullName": "Authentication Routes POST /api/auth/login should return validation error for missing password", "status": "passed", "title": "should return validation error for missing password", "duration": 5.195034000000305, "failureMessages": [], "location": {"line": 245, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/register"], "fullName": "Authentication Routes POST /api/auth/register should register user with valid data", "status": "passed", "title": "should register user with valid data", "duration": 5.5184619999999995, "failureMessages": [], "location": {"line": 259, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/register"], "fullName": "Authentication Routes POST /api/auth/register should return validation error for weak password", "status": "passed", "title": "should return validation error for weak password", "duration": 6.703728999999839, "failureMessages": [], "location": {"line": 293, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/register"], "fullName": "Authentication Routes POST /api/auth/register should return validation error for password mismatch", "status": "passed", "title": "should return validation error for password mismatch", "duration": 4.583123000000342, "failureMessages": [], "location": {"line": 308, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/logout"], "fullName": "Authentication Routes POST /api/auth/logout should logout user successfully", "status": "passed", "title": "should logout user successfully", "duration": 6.2832870000002, "failureMessages": [], "location": {"line": 325, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/refresh"], "fullName": "Authentication Routes POST /api/auth/refresh should refresh token successfully", "status": "passed", "title": "should refresh token successfully", "duration": 15.418901000000005, "failureMessages": [], "location": {"line": 343, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/refresh"], "fullName": "Authentication Routes POST /api/auth/refresh should return validation error for missing refresh token", "status": "passed", "title": "should return validation error for missing refresh token", "duration": 34.988806000000295, "failureMessages": [], "location": {"line": 363, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/forgot-password"], "fullName": "Authentication Routes POST /api/auth/forgot-password should send password reset email", "status": "passed", "title": "should send password reset email", "duration": 8.863792000000103, "failureMessages": [], "location": {"line": 375, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/forgot-password"], "fullName": "Authentication Routes POST /api/auth/forgot-password should return validation error for invalid email", "status": "passed", "title": "should return validation error for invalid email", "duration": 17.107718999999634, "failureMessages": [], "location": {"line": 393, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/reset-password"], "fullName": "Authentication Routes POST /api/auth/reset-password should reset password successfully", "status": "passed", "title": "should reset password successfully", "duration": 12.92148399999951, "failureMessages": [], "location": {"line": 407, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/reset-password"], "fullName": "Authentication Routes POST /api/auth/reset-password should return validation error for missing token", "status": "passed", "title": "should return validation error for missing token", "duration": 104.46808899999996, "failureMessages": [], "location": {"line": 427, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "GET /api/auth/me"], "fullName": "Authentication Routes GET /api/auth/me should get current user profile", "status": "passed", "title": "should get current user profile", "duration": 6.663624000000709, "failureMessages": [], "location": {"line": 442, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "PUT /api/auth/profile"], "fullName": "Authentication Routes PUT /api/auth/profile should update user profile", "status": "passed", "title": "should update user profile", "duration": 7.805420000000595, "failureMessages": [], "location": {"line": 467, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "PUT /api/auth/profile"], "fullName": "Authentication Routes PUT /api/auth/profile should return validation error for invalid email", "status": "passed", "title": "should return validation error for invalid email", "duration": 7.253821000000244, "failureMessages": [], "location": {"line": 499, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "PUT /api/auth/password"], "fullName": "Authentication Routes PUT /api/auth/password should change password successfully", "status": "passed", "title": "should change password successfully", "duration": 4.062050999999883, "failureMessages": [], "location": {"line": 514, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "PUT /api/auth/password"], "fullName": "Authentication Routes PUT /api/auth/password should return validation error for password mismatch", "status": "passed", "title": "should return validation error for password mismatch", "duration": 9.755584999999883, "failureMessages": [], "location": {"line": 540, "column": 5}, "meta": {}}], "startTime": 1752963627025, "endTime": 1752963627403.7556, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/routes/auth.test.js"}, {"assertionResults": [{"ancestorTitles": ["AuthService", "login"], "fullName": "AuthService login should login user with valid credentials", "status": "passed", "title": "should login user with valid credentials", "duration": 16.002354999999625, "failureMessages": [], "location": {"line": 87, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "login"], "fullName": "AuthService login should throw error for invalid credentials", "status": "passed", "title": "should throw error for invalid credentials", "duration": 3.0297090000003664, "failureMessages": [], "location": {"line": 112, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "login"], "fullName": "AuthService login should throw error when no record returned", "status": "passed", "title": "should throw error when no record returned", "duration": 0.58740299999954, "failureMessages": [], "location": {"line": 119, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "register"], "fullName": "AuthService register should register user with valid data", "status": "passed", "title": "should register user with valid data", "duration": 1.5418689999996786, "failureMessages": [], "location": {"line": 128, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "register"], "fullName": "AuthService register should handle registration validation errors", "status": "passed", "title": "should handle registration validation errors", "duration": 0.6324800000002142, "failureMessages": [], "location": {"line": 158, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "register"], "fullName": "AuthService register should continue registration even if verification email fails", "status": "passed", "title": "should continue registration even if verification email fails", "duration": 0.8487969999996494, "failureMessages": [], "location": {"line": 172, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "logout"], "fullName": "AuthService logout should logout successfully", "status": "passed", "title": "should logout successfully", "duration": 0.5735819999999876, "failureMessages": [], "location": {"line": 196, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "refreshToken"], "fullName": "AuthService refreshToken should refresh token when valid", "status": "passed", "title": "should refresh token when valid", "duration": 0.5847340000000258, "failureMessages": [], "location": {"line": 205, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "refreshToken"], "fullName": "AuthService refreshToken should throw error for invalid token", "status": "passed", "title": "should throw error for invalid token", "duration": 0.5609199999998964, "failureMessages": [], "location": {"line": 215, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "requestPasswordReset"], "fullName": "AuthService requestPasswordReset should request password reset", "status": "passed", "title": "should request password reset", "duration": 0.6156440000004295, "failureMessages": [], "location": {"line": 224, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "requestPasswordReset"], "fullName": "AuthService requestPasswordReset should return success even if email does not exist", "status": "passed", "title": "should return success even if email does not exist", "duration": 0.3240009999999529, "failureMessages": [], "location": {"line": 234, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "resetPassword"], "fullName": "AuthService resetPassword should reset password with valid token", "status": "passed", "title": "should reset password with valid token", "duration": 1.241331999999602, "failureMessages": [], "location": {"line": 245, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "resetPassword"], "fullName": "AuthService resetPassword should throw error for invalid token", "status": "passed", "title": "should throw error for invalid token", "duration": 0.4582250000003114, "failureMessages": [], "location": {"line": 257, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "getCurrentUser"], "fullName": "AuthService getCurrentUser should get current user profile", "status": "passed", "title": "should get current user profile", "duration": 0.7057620000005045, "failureMessages": [], "location": {"line": 269, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "getCurrentUser"], "fullName": "AuthService getCurrentUser should throw error for non-existent user", "status": "passed", "title": "should throw error for non-existent user", "duration": 0.6043440000003102, "failureMessages": [], "location": {"line": 290, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "updateProfile"], "fullName": "AuthService updateProfile should update user profile", "status": "passed", "title": "should update user profile", "duration": 3.459939000000304, "failureMessages": [], "location": {"line": 299, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "updateProfile"], "fullName": "AuthService updateProfile should handle validation errors", "status": "passed", "title": "should handle validation errors", "duration": 0.3909770000000208, "failureMessages": [], "location": {"line": 324, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "changePassword"], "fullName": "AuthService changePassword should change password successfully", "status": "passed", "title": "should change password successfully", "duration": 0.5259839999998803, "failureMessages": [], "location": {"line": 337, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "changePassword"], "fullName": "AuthService changePassword should throw error for incorrect current password", "status": "passed", "title": "should throw error for incorrect current password", "duration": 0.3199899999999616, "failureMessages": [], "location": {"line": 351, "column": 7}, "meta": {}}], "startTime": 1752963627701, "endTime": 1752963627735.32, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/services/authService.test.js"}, {"assertionResults": [{"ancestorTitles": ["PocketBase Service", "Initialization"], "fullName": "PocketBase Service Initialization should initialize with default URL when no environment variable is set", "status": "passed", "title": "should initialize with default URL when no environment variable is set", "duration": 2.555244999999559, "failureMessages": [], "location": {"line": 40, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Initialization"], "fullName": "PocketBase Service Initialization should load auth state from cookies on initialization", "status": "passed", "title": "should load auth state from cookies on initialization", "duration": 0.44686299999921175, "failureMessages": [], "location": {"line": 45, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Authentication Check"], "fullName": "PocketBase Service Authentication Check should return false when user is not authenticated", "status": "passed", "title": "should return false when user is not authenticated", "duration": 0.8501880000003439, "failureMessages": [], "location": {"line": 63, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Authentication Check"], "fullName": "PocketBase Service Authentication Check should return true when user is authenticated", "status": "passed", "title": "should return true when user is authenticated", "duration": 0.3284960000000865, "failureMessages": [], "location": {"line": 68, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Current User"], "fullName": "PocketBase Service Current User should return null when no user is authenticated", "status": "passed", "title": "should return null when no user is authenticated", "duration": 0.6026750000000902, "failureMessages": [], "location": {"line": 75, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Current User"], "fullName": "PocketBase Service Current User should return user data when authenticated", "status": "passed", "title": "should return user data when authenticated", "duration": 1.7648270000008779, "failureMessages": [], "location": {"line": 80, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "<PERSON><PERSON>"], "fullName": "PocketBase Service Login should successfully authenticate user with valid credentials", "status": "passed", "title": "should successfully authenticate user with valid credentials", "duration": 1.8300370000015391, "failureMessages": [], "location": {"line": 88, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "<PERSON><PERSON>"], "fullName": "PocketBase Service Login should return error when login fails", "status": "passed", "title": "should return error when login fails", "duration": 0.49215600000025006, "failureMessages": [], "location": {"line": 102, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Registration"], "fullName": "PocketBase Service Registration should successfully register a new user", "status": "passed", "title": "should successfully register a new user", "duration": 30.03727600000093, "failureMessages": [], "location": {"line": 114, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Registration"], "fullName": "PocketBase Service Registration should return error when registration fails", "status": "passed", "title": "should return error when registration fails", "duration": 0.4947179999999207, "failureMessages": [], "location": {"line": 128, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Logout"], "fullName": "PocketBase Service Logout should clear auth store on logout", "status": "passed", "title": "should clear auth store on logout", "duration": 0.3184389999987616, "failureMessages": [], "location": {"line": 140, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Collection Access"], "fullName": "PocketBase Service Collection Access should return collection reference", "status": "passed", "title": "should return collection reference", "duration": 0.3063720000009198, "failureMessages": [], "location": {"line": 147, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Real-time Subscriptions"], "fullName": "PocketBase Service Real-time Subscriptions should subscribe to real-time updates", "status": "passed", "title": "should subscribe to real-time updates", "duration": 0.4268420000007609, "failureMessages": [], "location": {"line": 155, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Real-time Subscriptions"], "fullName": "PocketBase Service Real-time Subscriptions should unsubscribe from real-time updates", "status": "passed", "title": "should unsubscribe from real-time updates", "duration": 0.2730700000010984, "failureMessages": [], "location": {"line": 164, "column": 7}, "meta": {}}, {"ancestorTitles": ["PocketBase Service", "Client Access"], "fullName": "PocketBase Service Client Access should return PocketBase client instance", "status": "passed", "title": "should return PocketBase client instance", "duration": 0.18216500000016822, "failureMessages": [], "location": {"line": 174, "column": 7}, "meta": {}}], "startTime": 1752963630970, "endTime": 1752963631016.1821, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/services/pocketbase.test.js"}, {"assertionResults": [{"ancestorTitles": ["SessionService", "createSession"], "fullName": "SessionService createSession should create a new session with valid data", "status": "passed", "title": "should create a new session with valid data", "duration": 3.502582999999504, "failureMessages": [], "location": {"line": 23, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "createSession"], "fullName": "SessionService createSession should generate unique session IDs", "status": "passed", "title": "should generate unique session IDs", "duration": 1.4924080000000686, "failureMessages": [], "location": {"line": 42, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getSession"], "fullName": "SessionService getSession should retrieve session by ID", "status": "passed", "title": "should retrieve session by ID", "duration": 0.4552619999994931, "failureMessages": [], "location": {"line": 51, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getSession"], "fullName": "SessionService getSession should return null for non-existent session", "status": "passed", "title": "should return null for non-existent session", "duration": 0.2796120000002702, "failureMessages": [], "location": {"line": 60, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getSessionByToken"], "fullName": "SessionService getSessionByToken should retrieve session by token", "status": "passed", "title": "should retrieve session by token", "duration": 0.46540400000048976, "failureMessages": [], "location": {"line": 67, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getSessionByToken"], "fullName": "SessionService getSessionByToken should return null for non-existent token", "status": "passed", "title": "should return null for non-existent token", "duration": 0.26223299999946903, "failureMessages": [], "location": {"line": 77, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getUserSessions"], "fullName": "SessionService getUserSessions should return all active sessions for a user", "status": "passed", "title": "should return all active sessions for a user", "duration": 1.3191309999983787, "failureMessages": [], "location": {"line": 84, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getUserSessions"], "fullName": "SessionService getUserSessions should return empty array for user with no sessions", "status": "passed", "title": "should return empty array for user with no sessions", "duration": 0.3978880000013305, "failureMessages": [], "location": {"line": 99, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "updateSessionActivity"], "fullName": "SessionService updateSessionActivity should update last activity for valid session", "status": "passed", "title": "should update last activity for valid session", "duration": 12.661659999999756, "failureMessages": [], "location": {"line": 106, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "updateSessionActivity"], "fullName": "SessionService updateSessionActivity should return false for non-existent token", "status": "passed", "title": "should return false for non-existent token", "duration": 0.3185140000005049, "failureMessages": [], "location": {"line": 121, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "updateSessionActivity"], "fullName": "SessionService updateSessionActivity should return false for inactive session", "status": "passed", "title": "should return false for inactive session", "duration": 0.37191900000107125, "failureMessages": [], "location": {"line": 126, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateSession"], "fullName": "SessionService invalidateSession should invalidate session by ID", "status": "passed", "title": "should invalidate session by ID", "duration": 0.2243269999999029, "failureMessages": [], "location": {"line": 139, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateSession"], "fullName": "SessionService invalidateSession should add token to blacklist when invalidating", "status": "passed", "title": "should add token to blacklist when invalidating", "duration": 0.19176800000059302, "failureMessages": [], "location": {"line": 149, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateSession"], "fullName": "SessionService invalidateSession should return false for non-existent session", "status": "passed", "title": "should return false for non-existent session", "duration": 0.15084999999999127, "failureMessages": [], "location": {"line": 159, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateSessionByToken"], "fullName": "SessionService invalidateSessionByToken should invalidate session by token", "status": "passed", "title": "should invalidate session by token", "duration": 0.27457700000013574, "failureMessages": [], "location": {"line": 166, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateSessionByToken"], "fullName": "SessionService invalidateSessionByToken should return false for non-existent token", "status": "passed", "title": "should return false for non-existent token", "duration": 0.1546490000000631, "failureMessages": [], "location": {"line": 178, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateAllUserSessions"], "fullName": "SessionService invalidateAllUserSessions should invalidate all sessions for a user", "status": "passed", "title": "should invalidate all sessions for a user", "duration": 0.44868099999985134, "failureMessages": [], "location": {"line": 185, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateAllUserSessions"], "fullName": "SessionService invalidateAllUserSessions should exclude specified token from invalidation", "status": "passed", "title": "should exclude specified token from invalidation", "duration": 0.37988599999880535, "failureMessages": [], "location": {"line": 203, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "isTokenBlacklisted"], "fullName": "SessionService isTokenBlacklisted should return true for blacklisted tokens", "status": "passed", "title": "should return true for blacklisted tokens", "duration": 0.23123800000030315, "failureMessages": [], "location": {"line": 221, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "isTokenBlacklisted"], "fullName": "SessionService isTokenBlacklisted should return false for non-blacklisted tokens", "status": "passed", "title": "should return false for non-blacklisted tokens", "duration": 0.3351069999989704, "failureMessages": [], "location": {"line": 230, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "validateSession"], "fullName": "SessionService validateSession should validate active session and update activity", "status": "passed", "title": "should validate active session and update activity", "duration": 0.3345150000004651, "failureMessages": [], "location": {"line": 239, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "validateSession"], "fullName": "SessionService validateSession should reject blacklisted tokens", "status": "passed", "title": "should reject blacklisted tokens", "duration": 0.21092700000008335, "failureMessages": [], "location": {"line": 250, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "validateSession"], "fullName": "SessionService validateSession should reject non-existent sessions", "status": "passed", "title": "should reject non-existent sessions", "duration": 0.19199900000057823, "failureMessages": [], "location": {"line": 262, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "validateSession"], "fullName": "SessionService validateSession should reject inactive sessions", "status": "passed", "title": "should reject inactive sessions", "duration": 0.19401099999959115, "failureMessages": [], "location": {"line": 269, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getSessionStats"], "fullName": "SessionService getSessionStats should return correct session statistics", "status": "passed", "title": "should return correct session statistics", "duration": 0.3797119999999268, "failureMessages": [], "location": {"line": 284, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "forceLogoutUser"], "fullName": "SessionService forceLogoutUser should force logout all sessions for a user", "status": "passed", "title": "should force logout all sessions for a user", "duration": 0.40000700000018696, "failureMessages": [], "location": {"line": 304, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getUserSessionDetails"], "fullName": "SessionService getUserSessionDetails should return detailed session information", "status": "passed", "title": "should return detailed session information", "duration": 0.791189999999915, "failureMessages": [], "location": {"line": 319, "column": 7}, "meta": {}}], "startTime": 1752963630726, "endTime": 1752963630753.7913, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/services/sessionService.test.js"}, {"assertionResults": [{"ancestorTitles": ["Authentication Store (TEST-001)", "Initial State"], "fullName": "Authentication Store (TEST-001) Initial State should initialize with correct defaults", "status": "passed", "title": "should initialize with correct defaults", "duration": 7.964421999999104, "failureMessages": [], "location": {"line": 47, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Getters"], "fullName": "Authentication Store (TEST-001) Getters should return correct isLoggedIn status", "status": "passed", "title": "should return correct isLoggedIn status", "duration": 1.7198989999997139, "failureMessages": [], "location": {"line": 59, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Getters"], "fullName": "Authentication Store (TEST-001) Getters should return current user", "status": "passed", "title": "should return current user", "duration": 1.4526869999990595, "failureMessages": [], "location": {"line": 68, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Login Action"], "fullName": "Authentication Store (TEST-001) Login Action should login successfully", "status": "passed", "title": "should login successfully", "duration": 2.715528999999151, "failureMessages": [], "location": {"line": 77, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Login Action"], "fullName": "Authentication Store (TEST-001) Login Action should handle login failure", "status": "passed", "title": "should handle login failure", "duration": 0.9096430000008695, "failureMessages": [], "location": {"line": 99, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Login Action"], "fullName": "Authentication Store (TEST-001) Login Action should handle login exception", "status": "passed", "title": "should handle login exception", "duration": 0.9414520000009361, "failureMessages": [], "location": {"line": 118, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Register Action"], "fullName": "Authentication Store (TEST-001) Register Action should register successfully", "status": "passed", "title": "should register successfully", "duration": 1.1605670000008104, "failureMessages": [], "location": {"line": 134, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Register Action"], "fullName": "Authentication Store (TEST-001) Register Action should handle registration failure", "status": "passed", "title": "should handle registration failure", "duration": 0.6351919999997335, "failureMessages": [], "location": {"line": 155, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Logout Action"], "fullName": "Authentication Store (TEST-001) Logout Action should logout successfully", "status": "passed", "title": "should logout successfully", "duration": 1.554626000001008, "failureMessages": [], "location": {"line": 173, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Token Refresh"], "fullName": "Authentication Store (TEST-001) Token Refresh should refresh token successfully", "status": "passed", "title": "should refresh token successfully", "duration": 0.8338949999997567, "failureMessages": [], "location": {"line": 194, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Token Refresh"], "fullName": "Authentication Store (TEST-001) Token Refresh should handle token refresh failure", "status": "passed", "title": "should handle token refresh failure", "duration": 0.5483670000012353, "failureMessages": [], "location": {"line": 210, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Password Reset"], "fullName": "Authentication Store (TEST-001) Password Reset should request password reset successfully", "status": "passed", "title": "should request password reset successfully", "duration": 1.016236999999819, "failureMessages": [], "location": {"line": 224, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Password Reset"], "fullName": "Authentication Store (TEST-001) Password Reset should reset password successfully", "status": "passed", "title": "should reset password successfully", "duration": 0.7227880000009463, "failureMessages": [], "location": {"line": 237, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "State Persistence"], "fullName": "Authentication Store (TEST-001) State Persistence should persist auth state to localStorage", "status": "passed", "title": "should persist auth state to localStorage", "duration": 0.8127460000014253, "failureMessages": [], "location": {"line": 252, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "State Persistence"], "fullName": "Authentication Store (TEST-001) State Persistence should restore auth state from localStorage", "status": "passed", "title": "should restore auth state from localStorage", "duration": 0.7955300000012357, "failureMessages": [], "location": {"line": 266, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "State Persistence"], "fullName": "Authentication Store (TEST-001) State Persistence should clear auth state", "status": "passed", "title": "should clear auth state", "duration": 0.8114779999996244, "failureMessages": [], "location": {"line": 283, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Initialization"], "fullName": "Authentication Store (TEST-001) Initialization should initialize auth state", "status": "passed", "title": "should initialize auth state", "duration": 0.6462819999997009, "failureMessages": [], "location": {"line": 300, "column": 7}, "meta": {}}], "startTime": 1752963630359, "endTime": 1752963630383.6462, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/auth.spec.js"}, {"assertionResults": [{"ancestorTitles": ["Tasks Store"], "fullName": "Tasks Store should initialize with correct defaults", "status": "passed", "title": "should initialize with correct defaults", "duration": 9.012668000000303, "failureMessages": [], "location": {"line": 26, "column": 5}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "addTask"], "fullName": "Tasks Store addTask should add a task successfully", "status": "passed", "title": "should add a task successfully", "duration": 5.683146000000306, "failureMessages": [], "location": {"line": 39, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "addTask"], "fullName": "Tasks Store addTask should handle errors when adding a task", "status": "passed", "title": "should handle errors when adding a task", "duration": 4.477275000000191, "failureMessages": [], "location": {"line": 63, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "addTask"], "fullName": "Tasks Store addTask should generate a task_id if not provided", "status": "passed", "title": "should generate a task_id if not provided", "duration": 1.264593999999306, "failureMessages": [], "location": {"line": 86, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "addTask"], "fullName": "Tasks Store addTask should use provided task_id if valid", "status": "passed", "title": "should use provided task_id if valid", "duration": 0.9083469999995941, "failureMessages": [], "location": {"line": 103, "column": 8}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "addTask"], "fullName": "Tasks Store addTask should throw error for invalid task_id format", "status": "passed", "title": "should throw error for invalid task_id format", "duration": 0.9321350000000166, "failureMessages": [], "location": {"line": 124, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "addTask"], "fullName": "Tasks Store addTask should throw error if task_id already exists", "status": "passed", "title": "should throw error if task_id already exists", "duration": 0.8656069999997271, "failureMessages": [], "location": {"line": 142, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "fetchTasks"], "fullName": "Tasks Store fetchTasks should fetch tasks successfully and populate the store", "status": "passed", "title": "should fetch tasks successfully and populate the store", "duration": 1.2192759999998088, "failureMessages": [], "location": {"line": 165, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "fetchTasks"], "fullName": "Tasks Store fetchTasks should set loading to true while fetching", "status": "passed", "title": "should set loading to true while fetching", "duration": 1.1324320000003354, "failureMessages": [], "location": {"line": 181, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "fetchTasks"], "fullName": "Tasks Store fetchTasks should handle errors when fetching tasks", "status": "passed", "title": "should handle errors when fetching tasks", "duration": 2.1244900000001508, "failureMessages": [], "location": {"line": 190, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "fetchTasks"], "fullName": "Tasks Store fetchTasks should pass filters to databaseService.getAllTasks", "status": "passed", "title": "should pass filters to databaseService.getAllTasks", "duration": 33.17925900000046, "failureMessages": [], "location": {"line": 203, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "updateTask"], "fullName": "Tasks Store updateTask should update a task successfully", "status": "passed", "title": "should update a task successfully", "duration": 1.218616000000111, "failureMessages": [], "location": {"line": 215, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "updateTask"], "fullName": "Tasks Store updateTask should handle task not found when updating", "status": "passed", "title": "should handle task not found when updating", "duration": 0.724566999999297, "failureMessages": [], "location": {"line": 234, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "updateTask"], "fullName": "Tasks Store updateTask should handle errors from databaseService when updating a task", "status": "passed", "title": "should handle errors from databaseService when updating a task", "duration": 0.8978440000000774, "failureMessages": [], "location": {"line": 247, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "updateTask"], "fullName": "Tasks Store updateTask should update a task successfully using PocketBase ID if task_id not found", "status": "passed", "title": "should update a task successfully using PocketBase ID if task_id not found", "duration": 1.3337369999999282, "failureMessages": [], "location": {"line": 269, "column": 8}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "deleteTask"], "fullName": "Tasks Store deleteTask should delete a task successfully", "status": "passed", "title": "should delete a task successfully", "duration": 9.75800799999979, "failureMessages": [], "location": {"line": 291, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "deleteTask"], "fullName": "Tasks Store deleteTask should handle task not found when deleting", "status": "passed", "title": "should handle task not found when deleting", "duration": 0.5817390000001978, "failureMessages": [], "location": {"line": 308, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "deleteTask"], "fullName": "Tasks Store deleteTask should handle errors from databaseService when deleting a task", "status": "passed", "title": "should handle errors from databaseService when deleting a task", "duration": 0.8447869999999966, "failureMessages": [], "location": {"line": 320, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "deleteTask"], "fullName": "Tasks Store deleteTask should delete a task successfully using PocketBase ID if task_id not found", "status": "passed", "title": "should delete a task successfully using PocketBase ID if task_id not found", "duration": 0.9255119999997987, "failureMessages": [], "location": {"line": 341, "column": 8}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "getTaskById"], "fullName": "Tasks Store getTaskById should get a task by its original ID successfully", "status": "passed", "title": "should get a task by its original ID successfully", "duration": 0.6885849999998754, "failureMessages": [], "location": {"line": 360, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "getTaskById"], "fullName": "Tasks Store getTaskById should return null if task is not found by original ID", "status": "passed", "title": "should return null if task is not found by original ID", "duration": 0.5905339999999342, "failureMessages": [], "location": {"line": 374, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "getTaskById"], "fullName": "Tasks Store getTaskById should handle errors from databaseService when getting a task by original ID", "status": "passed", "title": "should handle errors from databaseService when getting a task by original ID", "duration": 18.78135099999963, "failureMessages": [], "location": {"line": 385, "column": 7}, "meta": {}}], "startTime": 1752963627579, "endTime": 1752963627677.7812, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js"}]}