### Get CSRF token (required for authentication requests)
GET http://localhost:3001/api/auth/csrf-token

### Establish authenticated session
POST http://localhost:3001/api/auth/login
Content-Type: application/json
X-CSRF-Token: {{csrf_token}}

{
    "email": "<EMAIL>",
    "password": "admin123",
    "rememberMe": false
}

### Test authenticated endpoint (use token from login response)
GET http://localhost:3001/api/tasks
Authorization: Bearer {{auth_token}}
