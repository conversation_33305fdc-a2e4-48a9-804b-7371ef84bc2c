/**
 * Authentication Flows Integration Tests (TEST-005)
 * Tests for complete authentication flows (login, register, logout)
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { createPinia, setActivePinia } from 'pinia'
import { useAuthStore } from '../../ui/stores/auth.js'
import { pocketBaseService } from '../../common/services/pocketbase.js'

// Mock the pocketBaseService
vi.mock('../../common/services/pocketbase.js', () => ({
  pocketBaseService: {
    login: vi.fn(),
    register: vi.fn(),
    logout: vi.fn(),
    refreshToken: vi.fn(),
    requestPasswordReset: vi.fn(),
    resetPassword: vi.fn(),
    getCurrentUser: vi.fn(),
    updateProfile: vi.fn(),
    changePassword: vi.fn(),
    isAuthenticated: vi.fn(),
    getToken: vi.fn(),
  },
}))

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
})

// Mock router
const mockRouter = {
  push: vi.fn(),
  replace: vi.fn(),
  currentRoute: { value: { path: '/', query: {} } },
}

vi.mock('vue-router', () => ({
  useRouter: () => mockRouter,
  useRoute: () => mockRouter.currentRoute.value,
}))

describe('Authentication Flows Integration Tests (TEST-005)', () => {
  let authStore

  beforeEach(() => {
    setActivePinia(createPinia())
    authStore = useAuthStore()
    vi.clearAllMocks()
    localStorageMock.getItem.mockReturnValue(null)
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('Complete Login Flow', () => {
    it('should complete full login flow successfully', async () => {
      // Mock successful login response
      const mockUser = { id: '1', email: '<EMAIL>', name: 'Test User' }
      const mockToken = 'jwt-token-123'
      
      pocketBaseService.login.mockResolvedValue({
        success: true,
        user: mockUser,
        token: mockToken,
      })

      // Step 1: Initial state should be unauthenticated
      expect(authStore.isAuthenticated).toBe(false)
      expect(authStore.user).toBeNull()
      expect(authStore.token).toBeNull()

      // Step 2: Attempt login
      const credentials = { email: '<EMAIL>', password: 'password123' }
      const result = await authStore.login(credentials)

      // Step 3: Verify login was successful
      expect(result.success).toBe(true)
      expect(pocketBaseService.login).toHaveBeenCalledWith(credentials.email, credentials.password)

      // Step 4: Verify state was updated
      expect(authStore.isAuthenticated).toBe(true)
      expect(authStore.user).toEqual(mockUser)
      expect(authStore.token).toBe(mockToken)
      expect(authStore.error).toBeNull()

      // Step 5: Verify persistence
      expect(localStorageMock.setItem).toHaveBeenCalledWith('auth_token', mockToken)
      expect(localStorageMock.setItem).toHaveBeenCalledWith('auth_user', JSON.stringify(mockUser))
    })

    it('should handle login failure gracefully', async () => {
      // Mock failed login response
      pocketBaseService.login.mockResolvedValue({
        success: false,
        error: 'Invalid credentials',
      })

      // Step 1: Attempt login with invalid credentials
      const credentials = { email: '<EMAIL>', password: 'wrongpassword' }
      const result = await authStore.login(credentials)

      // Step 2: Verify login failed
      expect(result.success).toBe(false)
      expect(result.error).toBe('Invalid credentials')

      // Step 3: Verify state remains unauthenticated
      expect(authStore.isAuthenticated).toBe(false)
      expect(authStore.user).toBeNull()
      expect(authStore.token).toBeNull()
      expect(authStore.error).toBe('Invalid credentials')

      // Step 4: Verify no persistence occurred
      expect(localStorageMock.setItem).not.toHaveBeenCalled()
    })

    it('should handle network errors during login', async () => {
      // Mock network error
      pocketBaseService.login.mockRejectedValue(new Error('Network error'))

      // Step 1: Attempt login
      const credentials = { email: '<EMAIL>', password: 'password123' }
      const result = await authStore.login(credentials)

      // Step 2: Verify error handling
      expect(result.success).toBe(false)
      expect(authStore.error).toBe('Network error')
      expect(authStore.isAuthenticated).toBe(false)
    })
  })

  describe('Complete Registration Flow', () => {
    it('should complete full registration flow successfully', async () => {
      // Mock successful registration response
      const mockUser = { id: '1', email: '<EMAIL>', name: 'New User' }
      
      pocketBaseService.register.mockResolvedValue({
        success: true,
        user: mockUser,
      })

      // Step 1: Attempt registration
      const userData = {
        email: '<EMAIL>',
        password: 'password123',
        name: 'New User',
      }
      const result = await authStore.register(userData)

      // Step 2: Verify registration was successful
      expect(result.success).toBe(true)
      expect(pocketBaseService.register).toHaveBeenCalledWith(userData)
      expect(authStore.error).toBeNull()
    })

    it('should handle registration validation errors', async () => {
      // Mock validation error response
      pocketBaseService.register.mockResolvedValue({
        success: false,
        error: 'Email already exists',
      })

      // Step 1: Attempt registration with existing email
      const userData = {
        email: '<EMAIL>',
        password: 'password123',
        name: 'Test User',
      }
      const result = await authStore.register(userData)

      // Step 2: Verify error handling
      expect(result.success).toBe(false)
      expect(authStore.error).toBe('Email already exists')
    })
  })

  describe('Complete Logout Flow', () => {
    it('should complete full logout flow successfully', async () => {
      // Step 1: Set up authenticated state
      authStore.user = { id: '1', email: '<EMAIL>' }
      authStore.token = 'jwt-token-123'
      authStore.isAuthenticated = true

      // Mock successful logout
      pocketBaseService.logout.mockResolvedValue({ success: true })

      // Step 2: Perform logout
      await authStore.logout()

      // Step 3: Verify logout was called
      expect(pocketBaseService.logout).toHaveBeenCalled()

      // Step 4: Verify state was cleared
      expect(authStore.user).toBeNull()
      expect(authStore.token).toBeNull()
      expect(authStore.isAuthenticated).toBe(false)

      // Step 5: Verify localStorage was cleared
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('auth_token')
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('auth_user')
    })

    it('should handle logout errors gracefully', async () => {
      // Step 1: Set up authenticated state
      authStore.user = { id: '1', email: '<EMAIL>' }
      authStore.token = 'jwt-token-123'
      authStore.isAuthenticated = true

      // Mock logout error
      pocketBaseService.logout.mockRejectedValue(new Error('Logout failed'))

      // Step 2: Perform logout
      await authStore.logout()

      // Step 3: Verify state was still cleared despite error
      expect(authStore.user).toBeNull()
      expect(authStore.token).toBeNull()
      expect(authStore.isAuthenticated).toBe(false)
    })
  })

  describe('Password Reset Flow', () => {
    it('should complete password reset request flow', async () => {
      // Mock successful password reset request
      pocketBaseService.requestPasswordReset.mockResolvedValue({ success: true })

      // Step 1: Request password reset
      const email = '<EMAIL>'
      const result = await authStore.requestPasswordReset(email)

      // Step 2: Verify request was successful
      expect(result.success).toBe(true)
      expect(pocketBaseService.requestPasswordReset).toHaveBeenCalledWith(email)
    })

    it('should complete password reset confirmation flow', async () => {
      // Mock successful password reset
      pocketBaseService.resetPassword.mockResolvedValue({ success: true })

      // Step 1: Reset password with token
      const resetData = { token: 'reset-token-123', password: 'newpassword123' }
      const result = await authStore.resetPassword(resetData)

      // Step 2: Verify reset was successful
      expect(result.success).toBe(true)
      expect(pocketBaseService.resetPassword).toHaveBeenCalledWith(resetData)
    })
  })

  describe('Session Restoration Flow', () => {
    it('should restore session from localStorage on initialization', async () => {
      // Step 1: Mock stored auth data
      const mockUser = { id: '1', email: '<EMAIL>' }
      const mockToken = 'stored-token-123'

      localStorageMock.getItem.mockImplementation((key) => {
        if (key === 'auth_token') return mockToken
        if (key === 'auth_user') return JSON.stringify(mockUser)
        return null
      })

      pocketBaseService.isAuthenticated.mockReturnValue(true)
      pocketBaseService.getCurrentUser.mockResolvedValue({
        success: true,
        user: mockUser,
      })
      pocketBaseService.getToken.mockReturnValue(mockToken)

      // Step 2: Initialize auth
      await authStore.initializeAuth()

      // Step 3: Verify session was restored
      expect(authStore.user).toEqual(mockUser)
      expect(authStore.token).toBe(mockToken)
      expect(authStore.isAuthenticated).toBe(true)
      expect(authStore.initialized).toBe(true)
    })

    it('should handle invalid stored session gracefully', async () => {
      // Step 1: Mock invalid stored data
      localStorageMock.getItem.mockImplementation((key) => {
        if (key === 'auth_token') return 'invalid-token'
        if (key === 'auth_user') return 'invalid-json'
        return null
      })

      pocketBaseService.isAuthenticated.mockReturnValue(false)

      // Step 2: Initialize auth
      await authStore.initializeAuth()

      // Step 3: Verify session was not restored
      expect(authStore.user).toBeNull()
      expect(authStore.token).toBeNull()
      expect(authStore.isAuthenticated).toBe(false)
      expect(authStore.initialized).toBe(true)
    })
  })

  describe('Profile Management Flow', () => {
    it('should complete profile update flow', async () => {
      // Step 1: Set up authenticated state
      authStore.user = { id: '1', email: '<EMAIL>', name: 'Old Name' }
      authStore.isAuthenticated = true

      // Mock successful profile update
      const updatedUser = { id: '1', email: '<EMAIL>', name: 'New Name' }
      pocketBaseService.updateProfile.mockResolvedValue({
        success: true,
        user: updatedUser,
      })

      // Step 2: Update profile
      const profileData = { name: 'New Name' }
      const result = await authStore.updateProfile(profileData)

      // Step 3: Verify update was successful
      expect(result.success).toBe(true)
      expect(pocketBaseService.updateProfile).toHaveBeenCalledWith(profileData)
    })

    it('should complete password change flow', async () => {
      // Step 1: Set up authenticated state
      authStore.isAuthenticated = true

      // Mock successful password change
      pocketBaseService.changePassword.mockResolvedValue({ success: true })

      // Step 2: Change password
      const passwordData = { currentPassword: 'oldpass', newPassword: 'newpass123' }
      const result = await authStore.changePassword(passwordData)

      // Step 3: Verify change was successful
      expect(result.success).toBe(true)
      expect(pocketBaseService.changePassword).toHaveBeenCalledWith(passwordData)
    })
  })
})
