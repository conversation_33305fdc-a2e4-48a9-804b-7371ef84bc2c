/**
 * Error Handling and Edge Cases Integration Tests (TEST-010)
 * Tests for error handling and edge cases
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { createPinia, setActivePinia } from 'pinia'
import { useAuthStore } from '../../ui/stores/auth.js'
import { pocketBaseService } from '../../common/services/pocketbase.js'

// Mock the pocketBaseService
vi.mock('../../common/services/pocketbase.js', () => ({
  pocketBaseService: {
    login: vi.fn(),
    register: vi.fn(),
    logout: vi.fn(),
    refreshToken: vi.fn(),
    requestPasswordReset: vi.fn(),
    resetPassword: vi.fn(),
    getCurrentUser: vi.fn(),
    updateProfile: vi.fn(),
    changePassword: vi.fn(),
    isAuthenticated: vi.fn(),
    getToken: vi.fn(),
  },
}))

// Mock localStorage with error scenarios
const createMockLocalStorage = (shouldThrow = false) => ({
  getItem: vi.fn().mockImplementation((key) => {
    if (shouldThrow) throw new Error('localStorage not available')
    return null
  }),
  setItem: vi.fn().mockImplementation((key, value) => {
    if (shouldThrow) throw new Error('localStorage quota exceeded')
  }),
  removeItem: vi.fn().mockImplementation((key) => {
    if (shouldThrow) throw new Error('localStorage not available')
  }),
  clear: vi.fn(),
})

// Mock fetch with various error scenarios
const createMockFetch = (scenario) => {
  return vi.fn().mockImplementation(() => {
    switch (scenario) {
      case 'network-error':
        return Promise.reject(new Error('Network error'))
      case 'timeout':
        return new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Request timeout')), 100)
        })
      case 'server-error':
        return Promise.resolve({
          ok: false,
          status: 500,
          json: () => Promise.resolve({ error: 'Internal server error' })
        })
      case 'unauthorized':
        return Promise.resolve({
          ok: false,
          status: 401,
          json: () => Promise.resolve({ error: 'Unauthorized' })
        })
      case 'rate-limited':
        return Promise.resolve({
          ok: false,
          status: 429,
          json: () => Promise.resolve({ error: 'Too many requests' })
        })
      default:
        return Promise.resolve({
          ok: true,
          status: 200,
          json: () => Promise.resolve({ success: true })
        })
    }
  })
}

describe('Error Handling and Edge Cases Tests (TEST-010)', () => {
  let authStore
  let originalLocalStorage

  beforeEach(() => {
    setActivePinia(createPinia())
    authStore = useAuthStore()
    vi.clearAllMocks()
    
    // Store original localStorage
    originalLocalStorage = global.localStorage
  })

  afterEach(() => {
    vi.clearAllMocks()
    // Restore original localStorage
    global.localStorage = originalLocalStorage
  })

  describe('Network Error Handling', () => {
    it('should handle network errors during login', async () => {
      // Step 1: Mock network error
      pocketBaseService.login.mockRejectedValue(new Error('Network error'))

      // Step 2: Attempt login
      const result = await authStore.login({
        email: '<EMAIL>',
        password: 'password123'
      })

      // Step 3: Verify error handling
      expect(result.success).toBe(false)
      expect(authStore.error).toBe('Network error')
      expect(authStore.isAuthenticated).toBe(false)
      expect(authStore.loading).toBe(false)
    })

    it('should handle timeout errors', async () => {
      // Step 1: Mock timeout error
      pocketBaseService.login.mockImplementation(() => {
        return new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Request timeout')), 50)
        })
      })

      // Step 2: Attempt login
      const result = await authStore.login({
        email: '<EMAIL>',
        password: 'password123'
      })

      // Step 3: Verify timeout handling
      expect(result.success).toBe(false)
      expect(authStore.error).toBe('Request timeout')
    })

    it('should handle server errors gracefully', async () => {
      // Step 1: Mock server error
      pocketBaseService.register.mockResolvedValue({
        success: false,
        error: 'Internal server error'
      })

      // Step 2: Attempt registration
      const result = await authStore.register({
        email: '<EMAIL>',
        password: 'password123',
        name: 'Test User'
      })

      // Step 3: Verify server error handling
      expect(result.success).toBe(false)
      expect(authStore.error).toBe('Internal server error')
    })
  })

  describe('LocalStorage Error Handling', () => {
    it('should handle localStorage unavailability', async () => {
      // Step 1: Mock localStorage that throws errors
      global.localStorage = createMockLocalStorage(true)

      // Step 2: Set up authenticated state
      authStore.user = { id: '1', email: '<EMAIL>' }
      authStore.token = 'test-token'
      authStore.isAuthenticated = true

      // Step 3: Attempt to persist auth state
      expect(() => authStore.persistAuthState()).not.toThrow()

      // Step 4: Verify graceful handling
      expect(authStore.user).toEqual({ id: '1', email: '<EMAIL>' })
      expect(authStore.isAuthenticated).toBe(true)
    })

    it('should handle localStorage quota exceeded', async () => {
      // Step 1: Mock localStorage that throws quota error
      const mockStorage = createMockLocalStorage()
      mockStorage.setItem.mockImplementation(() => {
        throw new Error('QuotaExceededError')
      })
      global.localStorage = mockStorage

      // Step 2: Attempt to persist large data
      authStore.user = { id: '1', email: '<EMAIL>' }
      authStore.token = 'very-long-token'.repeat(1000)

      // Step 3: Verify graceful handling of quota error
      expect(() => authStore.persistAuthState()).not.toThrow()
    })

    it('should handle corrupted localStorage data', async () => {
      // Step 1: Mock localStorage with corrupted data
      const mockStorage = createMockLocalStorage()
      mockStorage.getItem.mockImplementation((key) => {
        if (key === 'auth_user') return 'invalid-json-data'
        if (key === 'auth_token') return 'valid-token'
        return null
      })
      global.localStorage = mockStorage

      // Step 2: Attempt to restore auth state
      expect(() => authStore.restoreAuthState()).not.toThrow()

      // Step 3: Verify graceful handling of corrupted data
      expect(authStore.user).toBeNull()
      expect(authStore.token).toBeNull()
      expect(authStore.isAuthenticated).toBe(false)
    })
  })

  describe('Authentication Edge Cases', () => {
    it('should handle simultaneous login attempts', async () => {
      // Step 1: Mock delayed login response
      let loginCallCount = 0
      pocketBaseService.login.mockImplementation(() => {
        loginCallCount++
        return new Promise(resolve => {
          setTimeout(() => {
            resolve({
              success: true,
              user: { id: loginCallCount, email: '<EMAIL>' },
              token: `token-${loginCallCount}`
            })
          }, 100)
        })
      })

      // Step 2: Make simultaneous login attempts
      const loginPromises = [
        authStore.login({ email: '<EMAIL>', password: 'password123' }),
        authStore.login({ email: '<EMAIL>', password: 'password123' }),
        authStore.login({ email: '<EMAIL>', password: 'password123' })
      ]

      const results = await Promise.all(loginPromises)

      // Step 3: Verify only one login succeeded or all handled gracefully
      const successfulLogins = results.filter(r => r.success)
      expect(successfulLogins.length).toBeGreaterThan(0)
      expect(authStore.isAuthenticated).toBe(true)
    })

    it('should handle login with empty credentials', async () => {
      // Step 1: Attempt login with empty credentials
      const result = await authStore.login({ email: '', password: '' })

      // Step 2: Verify validation or graceful handling
      expect(result.success).toBe(false)
      expect(authStore.error).toBeTruthy()
    })

    it('should handle login with null/undefined credentials', async () => {
      // Step 1: Attempt login with null credentials
      const result1 = await authStore.login({ email: null, password: null })
      const result2 = await authStore.login({ email: undefined, password: undefined })

      // Step 2: Verify graceful handling
      expect(result1.success).toBe(false)
      expect(result2.success).toBe(false)
      expect(authStore.isAuthenticated).toBe(false)
    })

    it('should handle extremely long input values', async () => {
      // Step 1: Create extremely long input values
      const longEmail = 'a'.repeat(1000) + '@example.com'
      const longPassword = 'p'.repeat(10000)

      // Step 2: Attempt login with long values
      const result = await authStore.login({
        email: longEmail,
        password: longPassword
      })

      // Step 3: Verify graceful handling (should not crash)
      expect(typeof result.success).toBe('boolean')
    })
  })

  describe('Token Refresh Edge Cases', () => {
    it('should handle token refresh when already refreshing', async () => {
      // Step 1: Set up authenticated state
      authStore.user = { id: '1', email: '<EMAIL>' }
      authStore.token = 'old-token'
      authStore.isAuthenticated = true

      // Step 2: Mock slow token refresh
      pocketBaseService.refreshToken.mockImplementation(() => {
        return new Promise(resolve => {
          setTimeout(() => {
            resolve({
              success: true,
              token: 'new-token',
              user: authStore.user
            })
          }, 200)
        })
      })

      // Step 3: Make simultaneous refresh requests
      const refreshPromises = [
        authStore.refreshToken(),
        authStore.refreshToken(),
        authStore.refreshToken()
      ]

      const results = await Promise.all(refreshPromises)

      // Step 4: Verify all requests handled gracefully
      results.forEach(result => {
        expect(result.success).toBe(true)
      })
      expect(authStore.token).toBe('new-token')
    })

    it('should handle token refresh with invalid response', async () => {
      // Step 1: Mock invalid refresh response
      pocketBaseService.refreshToken.mockResolvedValue({
        // Missing required fields
        success: true
        // No token or user
      })

      // Step 2: Attempt token refresh
      const result = await authStore.refreshToken()

      // Step 3: Verify graceful handling of invalid response
      expect(result.success).toBe(false)
      expect(authStore.error).toBeTruthy()
    })
  })

  describe('Session Management Edge Cases', () => {
    it('should handle session restoration with partial data', async () => {
      // Step 1: Mock partial localStorage data
      const mockStorage = createMockLocalStorage()
      mockStorage.getItem.mockImplementation((key) => {
        if (key === 'auth_token') return 'stored-token'
        if (key === 'auth_user') return null // Missing user data
        return null
      })
      global.localStorage = mockStorage

      // Step 2: Attempt session restoration
      authStore.restoreAuthState()

      // Step 3: Verify graceful handling
      expect(authStore.token).toBe('stored-token')
      expect(authStore.user).toBeNull()
      expect(authStore.isAuthenticated).toBe(false) // Should not be authenticated without user
    })

    it('should handle concurrent session modifications', async () => {
      // Step 1: Set up initial state
      authStore.user = { id: '1', email: '<EMAIL>' }
      authStore.token = 'token-1'
      authStore.isAuthenticated = true

      // Step 2: Simulate concurrent modifications
      const modifications = [
        () => { authStore.user = { id: '2', email: '<EMAIL>' } },
        () => { authStore.token = 'token-2' },
        () => { authStore.isAuthenticated = false },
        () => { authStore.clearAuthState() }
      ]

      // Step 3: Execute modifications concurrently
      await Promise.all(modifications.map(mod => Promise.resolve(mod())))

      // Step 4: Verify final state is consistent
      expect(typeof authStore.isAuthenticated).toBe('boolean')
      if (authStore.isAuthenticated) {
        expect(authStore.user).toBeTruthy()
        expect(authStore.token).toBeTruthy()
      }
    })
  })

  describe('Memory and Performance Edge Cases', () => {
    it('should handle memory pressure gracefully', async () => {
      // Step 1: Create large objects to simulate memory pressure
      const largeData = new Array(10000).fill(0).map((_, i) => ({
        id: i,
        data: 'x'.repeat(1000)
      }))

      // Step 2: Attempt operations under memory pressure
      authStore.user = { id: '1', email: '<EMAIL>', largeData }
      
      // Step 3: Verify operations still work
      expect(() => authStore.persistAuthState()).not.toThrow()
      expect(() => authStore.clearAuthState()).not.toThrow()
    })

    it('should handle rapid successive operations', async () => {
      // Step 1: Perform rapid operations
      const operations = []
      for (let i = 0; i < 100; i++) {
        operations.push(
          authStore.login({ email: `user${i}@example.com`, password: 'password' })
        )
      }

      // Step 2: Wait for all operations
      const results = await Promise.allSettled(operations)

      // Step 3: Verify no operations crashed
      results.forEach(result => {
        expect(result.status).toBe('fulfilled')
        expect(typeof result.value.success).toBe('boolean')
      })
    })
  })

  describe('Browser Compatibility Edge Cases', () => {
    it('should handle missing browser APIs gracefully', async () => {
      // Step 1: Mock missing localStorage
      const originalLocalStorage = global.localStorage
      delete global.localStorage

      // Step 2: Attempt operations without localStorage
      expect(() => authStore.persistAuthState()).not.toThrow()
      expect(() => authStore.restoreAuthState()).not.toThrow()

      // Step 3: Restore localStorage
      global.localStorage = originalLocalStorage
    })

    it('should handle disabled cookies', async () => {
      // Step 1: Mock cookie operations that fail
      Object.defineProperty(document, 'cookie', {
        get: () => {
          throw new Error('Cookies disabled')
        },
        set: () => {
          throw new Error('Cookies disabled')
        }
      })

      // Step 2: Attempt operations that might use cookies
      expect(() => authStore.initializeAuth()).not.toThrow()
    })
  })

  describe('Data Validation Edge Cases', () => {
    it('should handle malformed server responses', async () => {
      // Step 1: Mock malformed responses
      pocketBaseService.login.mockResolvedValue({
        // Malformed response structure
        result: 'success', // Wrong field name
        userData: { id: '1' }, // Wrong field name
        accessToken: 'token' // Wrong field name
      })

      // Step 2: Attempt login
      const result = await authStore.login({
        email: '<EMAIL>',
        password: 'password123'
      })

      // Step 3: Verify graceful handling
      expect(result.success).toBe(false)
      expect(authStore.error).toBeTruthy()
    })

    it('should handle unexpected data types', async () => {
      // Step 1: Mock responses with unexpected types
      pocketBaseService.register.mockResolvedValue({
        success: 'true', // String instead of boolean
        user: 'user-data', // String instead of object
        token: 12345 // Number instead of string
      })

      // Step 2: Attempt registration
      const result = await authStore.register({
        email: '<EMAIL>',
        password: 'password123',
        name: 'Test User'
      })

      // Step 3: Verify graceful handling
      expect(typeof result.success).toBe('boolean')
    })
  })
})
