/**
 * Authentication Store Unit Tests (TEST-001)
 * Tests for authentication store actions and mutations
 */

import { setActivePinia, createPinia } from 'pinia'
import { useAuthStore } from '../auth.js'
import { pocketBaseService } from '../../../common/services/pocketbase.js'
import { beforeEach, describe, expect, it, vi } from 'vitest'

// Mock the pocketBaseService
vi.mock('../../../common/services/pocketbase.js', () => ({
  pocketBaseService: {
    login: vi.fn(),
    register: vi.fn(),
    logout: vi.fn(),
    refreshToken: vi.fn(),
    requestPasswordReset: vi.fn(),
    resetPassword: vi.fn(),
    getCurrentUser: vi.fn(),
    updateProfile: vi.fn(),
    changePassword: vi.fn(),
    isAuthenticated: vi.fn(),
    getToken: vi.fn(),
  },
}))

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
})

describe('Authentication Store (TEST-001)', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()
    localStorageMock.getItem.mockReturnValue(null)
  })

  describe('Initial State', () => {
    it('should initialize with correct defaults', () => {
      const store = useAuthStore()
      expect(store.user).toBeNull()
      expect(store.token).toBeNull()
      expect(store.isAuthenticated).toBe(false)
      expect(store.loading).toBe(false)
      expect(store.error).toBeNull()
      expect(store.initialized).toBe(false)
    })
  })

  describe('Getters', () => {
    it('should return correct isLoggedIn status', () => {
      const store = useAuthStore()
      expect(store.isLoggedIn).toBe(false)

      store.isAuthenticated = true
      store.token = 'test-token'
      expect(store.isLoggedIn).toBe(true)
    })

    it('should return current user', () => {
      const store = useAuthStore()
      const testUser = { id: '1', email: '<EMAIL>' }
      store.user = testUser
      expect(store.currentUser).toEqual(testUser)
    })
  })

  describe('Login Action', () => {
    it('should login successfully', async () => {
      const store = useAuthStore()
      const credentials = { email: '<EMAIL>', password: 'password123' }
      const mockResponse = {
        success: true,
        user: { id: '1', email: '<EMAIL>' },
        token: 'test-token',
      }

      pocketBaseService.login.mockResolvedValue(mockResponse)

      const result = await store.login(credentials)

      expect(pocketBaseService.login).toHaveBeenCalledWith(credentials.email, credentials.password)
      expect(store.user).toEqual(mockResponse.user)
      expect(store.token).toBe(mockResponse.token)
      expect(store.isAuthenticated).toBe(true)
      expect(store.loading).toBe(false)
      expect(store.error).toBeNull()
      expect(result.success).toBe(true)
    })

    it('should handle login failure', async () => {
      const store = useAuthStore()
      const credentials = { email: '<EMAIL>', password: 'wrongpassword' }
      const mockResponse = {
        success: false,
        error: 'Invalid credentials',
      }

      pocketBaseService.login.mockResolvedValue(mockResponse)

      const result = await store.login(credentials)

      expect(store.user).toBeNull()
      expect(store.token).toBeNull()
      expect(store.isAuthenticated).toBe(false)
      expect(store.error).toBe('Invalid credentials')
      expect(result.success).toBe(false)
    })

    it('should handle login exception', async () => {
      const store = useAuthStore()
      const credentials = { email: '<EMAIL>', password: 'password123' }
      const errorMessage = 'Network error'

      pocketBaseService.login.mockRejectedValue(new Error(errorMessage))

      const result = await store.login(credentials)

      expect(store.error).toBe(errorMessage)
      expect(result.success).toBe(false)
      expect(store.loading).toBe(false)
    })
  })

  describe('Register Action', () => {
    it('should register successfully', async () => {
      const store = useAuthStore()
      const userData = {
        email: '<EMAIL>',
        password: 'password123',
        name: 'Test User',
      }
      const mockResponse = {
        success: true,
        user: { id: '1', email: '<EMAIL>', name: 'Test User' },
      }

      pocketBaseService.register.mockResolvedValue(mockResponse)

      const result = await store.register(userData)

      expect(pocketBaseService.register).toHaveBeenCalledWith(userData)
      expect(result.success).toBe(true)
      expect(store.error).toBeNull()
    })

    it('should handle registration failure', async () => {
      const store = useAuthStore()
      const userData = { email: '<EMAIL>', password: '123' }
      const mockResponse = {
        success: false,
        error: 'Password too short',
      }

      pocketBaseService.register.mockResolvedValue(mockResponse)

      const result = await store.register(userData)

      expect(store.error).toBe('Password too short')
      expect(result.success).toBe(false)
    })
  })

  describe('Logout Action', () => {
    it('should logout successfully', async () => {
      const store = useAuthStore()
      // Set initial authenticated state
      store.user = { id: '1', email: '<EMAIL>' }
      store.token = 'test-token'
      store.isAuthenticated = true

      pocketBaseService.logout.mockResolvedValue({ success: true })

      await store.logout()

      expect(pocketBaseService.logout).toHaveBeenCalled()
      expect(store.user).toBeNull()
      expect(store.token).toBeNull()
      expect(store.isAuthenticated).toBe(false)
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('auth_token')
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('auth_user')
    })
  })

  describe('Token Refresh', () => {
    it('should refresh token successfully', async () => {
      const store = useAuthStore()
      const mockResponse = {
        success: true,
        token: 'new-token',
        user: { id: '1', email: '<EMAIL>' },
      }

      pocketBaseService.refreshToken.mockResolvedValue(mockResponse)

      const result = await store.refreshToken()

      expect(store.token).toBe('new-token')
      expect(result.success).toBe(true)
    })

    it('should handle token refresh failure', async () => {
      const store = useAuthStore()
      const mockResponse = { success: false, error: 'Token expired' }

      pocketBaseService.refreshToken.mockResolvedValue(mockResponse)

      const result = await store.refreshToken()

      expect(result.success).toBe(false)
      expect(store.error).toBe('Token expired')
    })
  })

  describe('Password Reset', () => {
    it('should request password reset successfully', async () => {
      const store = useAuthStore()
      const email = '<EMAIL>'
      const mockResponse = { success: true }

      pocketBaseService.requestPasswordReset.mockResolvedValue(mockResponse)

      const result = await store.requestPasswordReset(email)

      expect(pocketBaseService.requestPasswordReset).toHaveBeenCalledWith(email)
      expect(result.success).toBe(true)
    })

    it('should reset password successfully', async () => {
      const store = useAuthStore()
      const resetData = { token: 'reset-token', password: 'newpassword123' }
      const mockResponse = { success: true }

      pocketBaseService.resetPassword.mockResolvedValue(mockResponse)

      const result = await store.resetPassword(resetData)

      expect(pocketBaseService.resetPassword).toHaveBeenCalledWith(resetData)
      expect(result.success).toBe(true)
    })
  })

  describe('State Persistence', () => {
    it('should persist auth state to localStorage', () => {
      const store = useAuthStore()
      store.user = { id: '1', email: '<EMAIL>' }
      store.token = 'test-token'

      store.persistAuthState()

      expect(localStorageMock.setItem).toHaveBeenCalledWith('auth_token', 'test-token')
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'auth_user',
        JSON.stringify({ id: '1', email: '<EMAIL>' })
      )
    })

    it('should restore auth state from localStorage', () => {
      const store = useAuthStore()
      const mockUser = { id: '1', email: '<EMAIL>' }

      localStorageMock.getItem.mockImplementation((key) => {
        if (key === 'auth_token') return 'stored-token'
        if (key === 'auth_user') return JSON.stringify(mockUser)
        return null
      })

      store.restoreAuthState()

      expect(store.token).toBe('stored-token')
      expect(store.user).toEqual(mockUser)
      expect(store.isAuthenticated).toBe(true)
    })

    it('should clear auth state', () => {
      const store = useAuthStore()
      store.user = { id: '1', email: '<EMAIL>' }
      store.token = 'test-token'
      store.isAuthenticated = true

      store.clearAuthState()

      expect(store.user).toBeNull()
      expect(store.token).toBeNull()
      expect(store.isAuthenticated).toBe(false)
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('auth_token')
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('auth_user')
    })
  })

  describe('Initialization', () => {
    it('should initialize auth state', async () => {
      const store = useAuthStore()
      pocketBaseService.isAuthenticated.mockReturnValue(true)
      pocketBaseService.getCurrentUser.mockResolvedValue({
        success: true,
        user: { id: '1', email: '<EMAIL>' },
      })
      pocketBaseService.getToken.mockReturnValue('stored-token')

      await store.initializeAuth()

      expect(store.initialized).toBe(true)
      expect(store.isAuthenticated).toBe(true)
    })
  })
})
