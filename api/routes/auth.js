/**
 * Authentication Routes
 * Defines route handlers for authentication API operations
 */

import express from 'express'
import rateLimit from 'express-rate-limit'
import { authValidationRules } from '../utils/validation.js'
import { authenticate, optionalAuthenticate } from '../middleware/auth.js'
import { asyncHandler } from '../middleware/errorHandler.js'
import { AuthService } from '../services/authService.js'
import { csrfProtection, generateCSRFToken, csrfTokenEndpoint } from '../middleware/csrf.js'
import { SessionService } from '../services/sessionService.js'
import {
  passwordResetRateLimit,
  emailResetRateLimit,
  progressiveDelayMiddleware,
  validateResetTokenMiddleware
} from '../middleware/passwordReset.js'

// Initialize new Router instance
export const authRoutes = express.Router()

// Rate limiting for authentication endpoints (disabled in test environment)
if (process.env.NODE_ENV !== 'test') {
  const authRateLimit = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5, // 5 attempts per window
    message: {
      success: false,
      error: {
        code: 'AUTH_RATE_LIMIT_EXCEEDED',
        message: 'Too many authentication attempts, please try again later.',
        timestamp: new Date().toISOString()
      }
    },
    standardHeaders: true,
    legacyHeaders: false,
    skip: (req) => {
      // Skip rate limiting for token refresh and logout
      return req.path === '/refresh' || req.path === '/logout'
    }
  })

  // Apply rate limiting to all auth routes
  authRoutes.use(authRateLimit)
}

// Apply CSRF protection to all auth routes
authRoutes.use(csrfProtection)

/**
 * @route GET /api/auth/csrf-token
 * @desc Get CSRF token for authentication requests
 * @access Public
 */
authRoutes.get('/csrf-token', csrfTokenEndpoint)

/**
 * @route POST /api/auth/login
 * @desc User login with email and password
 * @access Public
 */
authRoutes.post(
  '/login',
  authValidationRules.login,
  asyncHandler(async (req, res) => {
    const { email, password, rememberMe } = req.body
    const userAgent = req.headers['user-agent'] || ''
    const ip = req.ip || req.connection.remoteAddress || ''

    const result = await AuthService.login(email, password, rememberMe, userAgent, ip)

    // Generate CSRF token for authenticated session
    generateCSRFToken(req, res, () => {
      res.status(200).json({
        success: true,
        data: {
          user: result.user,
          token: result.token,
          refreshToken: result.refreshToken
        },
        message: 'Login successful'
      })
    })
  })
)

/**
 * @route POST /api/auth/register
 * @desc User registration
 * @access Public
 */
authRoutes.post(
  '/register',
  authValidationRules.register,
  asyncHandler(async (req, res) => {
    const { name, email, password, passwordConfirm } = req.body

    const result = await AuthService.register({
      name,
      email,
      password,
      passwordConfirm
    })

    res.status(201).json({
      success: true,
      data: {
        user: result.user
      },
      message: 'Registration successful. Please check your email to verify your account.'
    })
  })
)

/**
 * @route POST /api/auth/logout
 * @desc User logout
 * @access Private
 */
authRoutes.post(
  '/logout',
  authenticate,
  asyncHandler(async (req, res) => {
    const token = req.headers.authorization?.replace('Bearer ', '')

    const result = await AuthService.logout(token)

    res.status(200).json({
      success: true,
      message: result.message
    })
  })
)

/**
 * @route POST /api/auth/refresh
 * @desc Token refresh
 * @access Public
 */
authRoutes.post(
  '/refresh',
  authValidationRules.refresh,
  asyncHandler(async (req, res) => {
    const { refreshToken } = req.body

    const result = await AuthService.refreshToken(refreshToken)

    res.status(200).json({
      success: true,
      data: {
        token: result.token,
        refreshToken: result.refreshToken
      }
    })
  })
)

/**
 * @route POST /api/auth/forgot-password
 * @desc Password reset request
 * @access Public
 */
authRoutes.post(
  '/forgot-password',
  passwordResetRateLimit,
  emailResetRateLimit,
  progressiveDelayMiddleware,
  authValidationRules.forgotPassword,
  asyncHandler(async (req, res) => {
    const { email } = req.body

    const result = await AuthService.requestPasswordReset(email)

    res.status(200).json({
      success: true,
      message: result.message
    })
  })
)

/**
 * @route POST /api/auth/reset-password
 * @desc Password reset completion
 * @access Public
 */
authRoutes.post(
  '/reset-password',
  validateResetTokenMiddleware,
  authValidationRules.resetPassword,
  asyncHandler(async (req, res) => {
    const { token, password, passwordConfirm } = req.body

    const result = await AuthService.resetPassword(token, password, passwordConfirm)

    res.status(200).json({
      success: true,
      message: result.message
    })
  })
)

/**
 * @route GET /api/auth/me
 * @desc Get current user profile
 * @access Private
 */
authRoutes.get(
  '/me',
  authenticate,
  asyncHandler(async (req, res) => {
    const userId = req.user.id

    const result = await AuthService.getCurrentUser(userId)

    res.status(200).json({
      success: true,
      data: {
        user: result.user
      }
    })
  })
)

/**
 * @route PUT /api/auth/profile
 * @desc Update user profile
 * @access Private
 */
authRoutes.put(
  '/profile',
  authenticate,
  authValidationRules.updateProfile,
  asyncHandler(async (req, res) => {
    const userId = req.user.id
    const { name, email, avatar } = req.body

    const result = await AuthService.updateProfile(userId, {
      name,
      email,
      avatar
    })

    res.status(200).json({
      success: true,
      data: {
        user: result.user
      },
      message: 'Profile updated successfully'
    })
  })
)

/**
 * @route PUT /api/auth/password
 * @desc Change user password
 * @access Private
 */
authRoutes.put(
  '/password',
  authenticate,
  authValidationRules.changePassword,
  asyncHandler(async (req, res) => {
    const userId = req.user.id
    const { currentPassword, newPassword, newPasswordConfirm } = req.body

    const result = await AuthService.changePassword(
      userId,
      currentPassword,
      newPassword,
      newPasswordConfirm
    )

    res.status(200).json({
      success: true,
      message: result.message
    })
  })
)

/**
 * @route GET /api/auth/sessions
 * @desc Get current user's active sessions
 * @access Private
 */
authRoutes.get(
  '/sessions',
  authenticate,
  asyncHandler(async (req, res) => {
    const userId = req.user.id
    const currentToken = req.headers.authorization?.replace('Bearer ', '')

    const sessions = SessionService.getUserSessionDetails(userId)

    // Mark current session
    const sessionsWithCurrent = sessions.map(session => ({
      ...session,
      isCurrent: SessionService.getSessionByToken(currentToken)?.id === session.id
    }))

    res.status(200).json({
      success: true,
      data: {
        sessions: sessionsWithCurrent
      }
    })
  })
)

/**
 * @route DELETE /api/auth/sessions/:sessionId
 * @desc Invalidate a specific session
 * @access Private
 */
authRoutes.delete(
  '/sessions/:sessionId',
  authenticate,
  asyncHandler(async (req, res) => {
    const { sessionId } = req.params
    const userId = req.user.id

    // Verify session belongs to user
    const session = SessionService.getSession(sessionId)
    if (!session || session.userId !== userId) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'NOT_FOUND',
          message: 'Session not found',
          timestamp: new Date().toISOString()
        }
      })
    }

    const invalidated = SessionService.invalidateSession(sessionId)

    res.status(200).json({
      success: true,
      message: invalidated ? 'Session invalidated successfully' : 'Session was already inactive'
    })
  })
)

/**
 * @route DELETE /api/auth/sessions
 * @desc Invalidate all sessions except current
 * @access Private
 */
authRoutes.delete(
  '/sessions',
  authenticate,
  asyncHandler(async (req, res) => {
    const userId = req.user.id
    const currentToken = req.headers.authorization?.replace('Bearer ', '')

    const invalidatedCount = SessionService.invalidateAllUserSessions(userId, currentToken)

    res.status(200).json({
      success: true,
      message: `${invalidatedCount} sessions invalidated successfully`
    })
  })
)

/**
 * @route POST /api/auth/extend-session
 * @desc Extend current session timeout
 * @access Private
 */
authRoutes.post(
  '/extend-session',
  authenticate,
  asyncHandler(async (req, res) => {
    const token = req.headers.authorization?.replace('Bearer ', '')

    // Update session activity
    const updated = SessionService.updateSessionActivity(token)

    if (!updated) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'SESSION_NOT_FOUND',
          message: 'Session not found or expired',
          timestamp: new Date().toISOString()
        }
      })
    }

    res.status(200).json({
      success: true,
      message: 'Session extended successfully',
      data: {
        extendedAt: new Date().toISOString()
      }
    })
  })
)

/**
 * @route GET /api/auth/session-status
 * @desc Get current session timeout status
 * @access Private
 */
authRoutes.get(
  '/session-status',
  authenticate,
  asyncHandler(async (req, res) => {
    const token = req.headers.authorization?.replace('Bearer ', '')
    const session = SessionService.getSessionByToken(token)

    if (!session) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'SESSION_NOT_FOUND',
          message: 'Session not found',
          timestamp: new Date().toISOString()
        }
      })
    }

    const now = Date.now()
    const sessionAge = now - session.lastActivity.getTime()
    const timeoutDuration = 30 * 60 * 1000 // 30 minutes
    const timeRemaining = Math.max(0, timeoutDuration - sessionAge)

    res.status(200).json({
      success: true,
      data: {
        sessionId: session.id,
        lastActivity: session.lastActivity,
        timeRemaining,
        isNearTimeout: timeRemaining <= 5 * 60 * 1000, // 5 minutes
        expiresAt: new Date(session.lastActivity.getTime() + timeoutDuration)
      }
    })
  })
)
